[{"name": "test", "ip": "localhost", "port": 9876, "hbInterval": 30, "senderCompId": "e2e_haywood_pacocha_2960", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "garanti-uat", "ip": "************", "port": 9876, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "demo", "ip": "*************", "port": 9876, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "qa-fix-market-data", "ip": "**************", "port": 9877, "hbInterval": 30, "senderCompId": "denis", "targetCompId": "ATFIXMARKET", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "qa-fix-drop-copy", "ip": "**************", "port": 9878, "hbInterval": 30, "senderCompId": "garantitest", "targetCompId": "ATFIXDROPCOPY", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "qa-fix-trading", "ip": "*************", "port": 9876, "hbInterval": 30, "senderCompId": "denis", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "qa-fix-custom-ohlc", "ip": "**************", "port": 9879, "hbInterval": 30, "senderCompId": "denis", "targetCompId": "ATFIXOHLC", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "runner-fix-trading", "ip": "qwe", "port": 9876, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "runner-fix-ohlc", "ip": "qwe", "port": 9879, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXOHLC", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "qa-fix-market-data_trader", "ip": "*************", "port": 9877, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXMARKET", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "garanti-uat-market-data", "ip": "***********", "port": 9877, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXMARKET", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "garanti-uat-custom-ohlc", "ip": "***********", "port": 9879, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXOHLC", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "garanti-uat-drop-copy", "ip": "***********", "port": 9878, "senderCompId": "garantitest", "targetCompId": "ATFIXDROPCOPY", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "garanti-uat-trading", "ip": "************", "port": 9876, "senderCompId": "trader", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "uat trading", "ip": "**************", "port": 9876, "senderCompId": "garanti", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "uat dropcopy", "ip": "************", "port": 9878, "senderCompId": "garanti", "targetCompId": "ATFIXDROPCOPY", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "local trading", "ip": "localhost", "port": 9876, "hbInterval": 30, "senderCompId": "admin", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "local market-data", "ip": "localhost", "port": 9877, "hbInterval": 30, "senderCompId": "trader", "targetCompId": "ATFIXMARKETDATA", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "local drop-copy", "ip": "localhost", "port": 9878, "hbInterval": 30, "senderCompId": "admin", "targetCompId": "ATFIXDROPCOPY", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "dev-trading (IP)", "ip": "**************", "port": 9876, "senderCompId": "joseph_the_banker", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml", "sslEnabled": false}, {"name": "dev-market-data (IP)", "ip": "*************", "port": 9877, "hbInterval": 30, "senderCompId": "dkouzan", "targetCompId": "ATFIXMARKETDATA", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml"}, {"name": "dev-trading (ssl)", "ip": "wyden-dev-fix-api.wyden.io", "port": 9876, "senderCompId": "joseph_the_banker", "targetCompId": "ATFIXSERVER", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml", "sslEnabled": true}, {"name": "dev drop-copy (ssl)", "ip": "wyden-dev-fix-api-drop-copy.wyden.io", "port": 9878, "senderCompId": "joseph_the_banker", "targetCompId": "ATFIXDROPCOPY", "fixVersion": "4", "dictionaryLocation": "FIX44-Wyden.xml", "sslEnabled": true}]