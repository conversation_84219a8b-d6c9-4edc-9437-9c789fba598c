syntax = "proto3";
option java_multiple_files = true;

package io.wyden.published.referencedata;

message VenueAccount {
  string id = 1;
  string venue_account_name = 8;
  string owner_username = 2;
  string venue_name = 3;
  string archived_at = 4;  // ISO 8601 format, UTC
  string suspended_at = 5;  // ISO 8601 format, UTC
  string created_at = 6;  // ISO 8601 format, UTC
  AccountType account_type = 7;
  WalletType wallet_type = 9;
}

enum AccountType {
  ACCOUNT_TYPE_UNSPECIFIED = 0;
  WALLET = 1;
  EXCHANGE = 2;
  CUSTODY = 3;
  ACCOUNT_TYPE_CLOB = 4;
  reserved 5;
}

enum WalletType {
  WALLET_TYPE_UNSPECIFIED = 0;
  WALLET_VOSTRO = 1;
  WALLET_NOSTRO = 2;
}
