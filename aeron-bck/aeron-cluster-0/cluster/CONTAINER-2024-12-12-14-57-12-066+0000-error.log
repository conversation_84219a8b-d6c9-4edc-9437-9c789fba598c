
1 observations from 2024-12-12 14:16:51.663+0000 to 2024-12-12 14:16:51.663+0000 for:
io.aeron.exceptions.ConductorServiceTimeoutException: FATAL - service interval exceeded: timeout=3600000000000ns, interval=3627015675904ns
	at io.aeron.ClientConductor.checkServiceInterval(ClientConductor.java:1577)
	at io.aeron.ClientConductor.checkTimeouts(ClientConductor.java:1561)
	at io.aeron.ClientConductor.service(ClientConductor.java:1470)
	at io.aeron.ClientConductor.doWork(ClientConductor.java:196)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2024-12-12 14:16:51.690+0000 to 2024-12-12 14:16:51.690+0000 for:
org.agrona.concurrent.AgentTerminationException
	at io.aeron.ClientConductor.doWork(ClientConductor.java:193)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2024-12-12 14:16:51.693+0000 to 2024-12-12 14:16:51.693+0000 for:
org.agrona.concurrent.AgentTerminationException: unexpected Aeron close
	at io.aeron.cluster.service.ClusteredServiceAgent.checkForClockTick(ClusteredServiceAgent.java:1070)
	at io.aeron.cluster.service.ClusteredServiceAgent.doIdleWork(ClusteredServiceAgent.java:420)
	at io.aeron.cluster.service.ClusteredServiceAgent.idle(ClusteredServiceAgent.java:407)
	at io.wyden.aeron.node.AeronClusteredService.onStart(AeronClusteredService.java:76)
	at io.aeron.cluster.service.ClusteredServiceAgent.loadSnapshot(ClusteredServiceAgent.java:903)
	at io.aeron.cluster.service.ClusteredServiceAgent.recoverState(ClusteredServiceAgent.java:754)
	at io.aeron.cluster.service.ClusteredServiceAgent.onStart(ClusteredServiceAgent.java:182)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:150)
	at java.base/java.lang.Thread.run(Unknown Source)


3 distinct errors observed.
