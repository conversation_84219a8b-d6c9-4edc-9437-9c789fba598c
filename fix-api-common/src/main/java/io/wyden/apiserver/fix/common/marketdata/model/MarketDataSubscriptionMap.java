package io.wyden.apiserver.fix.common.marketdata.model;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.cloudutils.telemetry.Telemetry;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class MarketDataSubscriptionMap {

    private final Map<MarketDataSubscriptionKey, MarketDataSubscription> marketDataSubscriptionMap = new ConcurrentHashMap<>();

    public MarketDataSubscriptionMap(Telemetry telemetry, String side) {
        MeterRegistry meterRegistry = telemetry.getMeterRegistry();
        meterRegistry.gaugeMapSize("wyden.market-data.subscriptions", Tags.of("side", side), marketDataSubscriptionMap);
    }

    public MarketDataSubscription put(MarketDataSubscriptionKey marketDataSubscriptionKey, MarketDataSubscription marketDataSubscription) {
        return marketDataSubscriptionMap.put(marketDataSubscriptionKey, marketDataSubscription);
    }

    public MarketDataSubscription remove(MarketDataSubscriptionKey subscriptionKey) {
        return marketDataSubscriptionMap.remove(subscriptionKey);
    }

    public Map<MarketDataSubscriptionKey, MarketDataSubscription> getSubscriptionMap() {
        return Collections.unmodifiableMap(marketDataSubscriptionMap);
    }

    public MarketDataSubscription get(MarketDataSubscriptionKey subscriptionKey) {
        return marketDataSubscriptionMap.get(subscriptionKey);
    }

    public Optional<MarketDataSubscription> findByRequestId(String requestId) {
        return marketDataSubscriptionMap.values().stream()
            .filter(s -> Objects.equals(s.getRequestId(), requestId))
            .findFirst();
    }

    public void clear() {
        marketDataSubscriptionMap.clear();
    }

    public List<MarketDataSubscription> findByPortfolioIdInstrumentId(String portfolioId, String instrumentId) {
        return marketDataSubscriptionMap.entrySet().stream()
            .filter(e -> portfolioId.equals(e.getKey().portfolioId()) && instrumentId.equals(e.getKey().instrumentId()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toList());
    }

    public List<MarketDataSubscription> findByPortfolioId(String portfolioId) {
        return marketDataSubscriptionMap.entrySet().stream()
            .filter(e -> portfolioId.equals(e.getKey().portfolioId()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toList());
    }
}
