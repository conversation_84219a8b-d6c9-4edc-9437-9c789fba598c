package io.wyden.apiserver.fix.common.marketdata;

import io.wyden.apiserver.fix.common.dictionary.WydenAccount;
import io.wyden.apiserver.fix.common.dictionary.WydenPortfolio;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataDepth;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataSubscriptionKey;
import io.wyden.apiserver.fix.common.marketdata.model.SessionScopedMDRequest;
import io.wyden.apiserver.fix.common.referencedata.InstrumentsRepository;
import io.wyden.apiserver.fix.common.security.AccessService;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import quickfix.FieldNotFound;
import quickfix.Group;
import quickfix.IncorrectTagValue;
import quickfix.SessionID;
import quickfix.field.MDEntryType;
import quickfix.field.MDUpdateType;
import quickfix.field.SecurityID;
import quickfix.field.Text;
import quickfix.fix44.MarketDataRequest;
import quickfix.fix44.MarketDataRequestReject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Component
@ConditionalOnBean(name = "marketDataRequestExchange")
public class MarketDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataService.class);

    private final MarketDataSessionService marketDataSessionService;
    private final FixSessionWrapper fixSessionWrapper;
    private final InstrumentsRepository instrumentsRepository;
    private final AccessService accessService;

    public MarketDataService(MarketDataSessionService marketDataSessionService, FixSessionWrapper fixSessionWrapper, InstrumentsRepository instrumentsRepository, AccessService accessService) {
        this.marketDataSessionService = marketDataSessionService;
        this.fixSessionWrapper = fixSessionWrapper;
        this.instrumentsRepository = instrumentsRepository;
        this.accessService = accessService;
    }

    public void subscribeMarketData(SessionID sessionId, MarketDataRequest marketDataRequest) throws FieldNotFound, IncorrectTagValue, IllegalArgumentException {
        LOGGER.info("{} - Subscribe market data request: {}", sessionId, marketDataRequest);

        MDUpdateType mdUpdateType = marketDataRequest.getMDUpdateType();
        if (mdUpdateType.getValue() != MDUpdateType.FULL_REFRESH) {
            throw new IllegalArgumentException("Only full refresh is supported");
        }
        String mdReqId = marketDataRequest.getMDReqID().getValue();
        List<MarketDataSubscriptionKey> subscriptionKeys = collectSubscriptionKeys(marketDataRequest);
        Set<MarketDataType> types = collectEntryTypes(marketDataRequest);
        MarketDataDepth marketDepth = MarketDataDepth.fromMarketDepth(marketDataRequest.getMarketDepth().getValue());

        checkPermissions(sessionId, subscriptionKeys);

        marketDataSessionService.subscribe(new SessionScopedMDRequest(sessionId, mdReqId), subscriptionKeys, types, marketDepth);
    }

    private void checkPermissions(SessionID sessionId, List<MarketDataSubscriptionKey> symbolsEntries) {
        symbolsEntries.forEach(subscriptionKey -> {
            if (subscriptionKey.venueAccount() != null) {
                if (accessService.hasStreetSideMarketDataAccess(sessionId, subscriptionKey.venueAccount())) {
                    validateInstrument(subscriptionKey.instrumentId(), VenueType.STREET, VenueType.CLOB);
                } else {
                    throw new MarketDataAccessDeniedException("User with clientId: " + sessionId.getTargetCompID() +
                        " have no access to market data subscribing via venueAccount: " + subscriptionKey.venueAccount());
                }
            }

            if (subscriptionKey.portfolioId() != null) {
                if (accessService.hasClientSideMarketDataAccess(sessionId)) {
                    validateInstrument(subscriptionKey.instrumentId(), VenueType.CLIENT);
                } else {
                    throw new MarketDataAccessDeniedException("User with clientId: " + sessionId.getTargetCompID() +
                        " have no access to market data subscribing via portfolio: " + subscriptionKey.portfolioId());
                }
            }

        });
    }

    private void validateInstrument(String symbol, VenueType... expectedVenueTypes) throws IllegalArgumentException {
        Instrument instrument = Optional
            .ofNullable(instrumentsRepository.find(symbol))
            .orElseThrow(() -> new IllegalArgumentException("Instrument not found for given instrumentId: %s".formatted(symbol)));

        VenueType instrumentVenueType = instrument.getBaseInstrument().getVenueType();
        if (Arrays.stream(expectedVenueTypes).noneMatch(instrumentVenueType::equals)) {
            throw new IllegalArgumentException("%s instrument expected, but requested %s: %s"
                .formatted(expectedVenueTypes, instrumentVenueType, symbol));
        }
    }

    private Set<MarketDataType> collectEntryTypes(MarketDataRequest marketDataRequest) throws FieldNotFound {
        Set<Character> types = new HashSet<>();
        int noMdEntryTypeTag = new MarketDataRequest.NoMDEntryTypes().getFieldTag();
        for (Group group : marketDataRequest.getGroups(noMdEntryTypeTag)) {
            types.add(group.getField(new MDEntryType()).getValue());
        }
        return MarketDataType.fromMDEntryType(types);
    }

    private List<MarketDataSubscriptionKey> collectSubscriptionKeys(MarketDataRequest marketDataRequest) throws FieldNotFound, IllegalArgumentException, IncorrectTagValue {
        List<MarketDataSubscriptionKey> response = new ArrayList<>();
        MarketDataDepth marketDepth = MarketDataDepth.fromMarketDepth(marketDataRequest.getMarketDepth().getValue());
        int noRelatedSymTag = new MarketDataRequest.NoRelatedSym().getFieldTag();
        for (Group group : marketDataRequest.getGroups(noRelatedSymTag)) {
            String instrumentId = group.getField(new SecurityID()).getValue();
            String venueAccount = group.getOptionalString(new WydenAccount().getField()).orElse(null);
            String portfolioId = group.getOptionalString(new WydenPortfolio().getField()).orElse(null);
            response.add(new MarketDataSubscriptionKey(instrumentId, venueAccount, portfolioId, marketDepth));
        }
        return response;
    }

    public void unsubscribeMarketData(SessionID sessionId, MarketDataRequest marketDataRequest) throws FieldNotFound {
        LOGGER.trace("Unsubscribe market data");
        String mdReqId = marketDataRequest.getMDReqID().getValue();

        LOGGER.info("Got FIX market data UNSUBSCRIBE request for sessionId {}.", sessionId);
        marketDataSessionService.unsubscribe(new SessionScopedMDRequest(sessionId, mdReqId));
    }

    public void rejectMessage(SessionID sessionId, MarketDataRequest message, Exception e) {
        LOGGER.error("Session message error ({}) - producing early MarketDataRequestReject", sessionId, e);
        MarketDataRequestReject reject = createReject(message, e);
        fixSessionWrapper.send(sessionId, reject);
    }

    private MarketDataRequestReject createReject(MarketDataRequest message, Exception originalError) {
        try {
            MarketDataRequestReject reject = new MarketDataRequestReject(message.getMDReqID());
            reject.set(new Text(originalError.getMessage()));
            return reject;
        } catch (FieldNotFound e) {
            // should not happen, such Message would be rejected by Quickfixj early
            throw new IllegalStateException("MDReqID absent, cannot produce MarketDataReject message", e);
        }
    }
}
