package io.wyden.apiserver.fix.common.marketdata.infrastructure;

import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.queue.AutoAckMessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static io.wyden.cloudutils.rabbitmq.queue.MatchingCondition.ALL;


public class MarketDataQueueBinder<T extends Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataQueueBinder.class);

    public MarketDataQueueBinder(RabbitExchange<T> dataExchange, RabbitQueue<T> queue, Parser<T> parser, AutoAckMessageConsumer<T> consumer) {
        queue.attachConsumerAutoAck((d, t) -> parser.parseFrom(d), consumer);
        LOGGER.info("Binding queue {} with exchange {}. Matching ALL headers.", queue, dataExchange);
        queue.bindWithHeaders(dataExchange, ALL, Map.of());
    }
}
