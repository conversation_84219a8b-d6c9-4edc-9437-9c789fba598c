package io.wyden.executionengine.domain.model;

import com.google.common.base.MoreObjects;
import io.wyden.cloudutils.tools.MathUtils;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;

import java.io.Serializable;
import java.math.BigDecimal;

public class BookEntry implements Serializable {

    private final OemsRequest order;
    private final BigDecimal price;
    private BigDecimal remainingQuantity;
    private BigDecimal executedAmount;
    private final long timestamp;

    public BookEntry(OemsRequest order) {
        this.order = order;
        this.price = OrderUtils.getPrice(order.getPrice(), order.getOrderType());
        this.remainingQuantity = getOrderQuantity();
        this.executedAmount = BigDecimal.ZERO;
        this.timestamp = System.currentTimeMillis();
    }

    public boolean cantExecute(BigDecimal limit) {
        if (order.getOrderType() == OemsOrderType.MARKET) {
            return false;
        }

        if (order.getOrderType() == OemsOrderType.LIMIT) {
            if (OrderUtils.isBuyOrder(order.getSide())) {
                return new BigDecimal(order.getPrice()).compareTo(limit) < 0;
            } else {
                return new BigDecimal(order.getPrice()).compareTo(limit) > 0;
            }
        }

        throw new UnsupportedOperationException("Order type %s not supported".formatted(order.getOrderType()));
    }

    public Execution execute(BigDecimal quantity, BigDecimal price) {
        BigDecimal executedQuantity;

        if (remainingQuantity.compareTo(quantity) >= 0) {
            executedQuantity = quantity;
            remainingQuantity = remainingQuantity.subtract(quantity);
        } else {
            executedQuantity = remainingQuantity;
            remainingQuantity = BigDecimal.ZERO;
        }
        executedAmount = executedAmount.add(executedQuantity.multiply(price));
        return new Execution(order, executedQuantity, remainingQuantity, price, getAvgPrice());
    }

    public OemsRequest getOrder() {
        return order;
    }

    /**
     * @return Limit price as in originating Order. If can be executed at any price (Market Orders), its value will be BigDecimal.ZERO
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * @return Quantity (in base currency for FX or number of contracts in non-FX) that still remains to be filled
     */
    public BigDecimal getRemainingQuantity() {
        return remainingQuantity;
    }

    /**
     * @return Total executed amount (quantity of base currency or contracts * execution price). Denominated in quote currency.
     */
    public BigDecimal getExecutedAmount() {
        return executedAmount;
    }

    /**
     * @return Overall requested quantity (of base currency for FX or number of contracts for non-FX) on given Order in the entry
     */
    public BigDecimal getOrderQuantity() {
        return new BigDecimal(order.getQuantity());
    }

    /**
     * @return Cumulative quantity (of base currency for FX or number of contracts for non-FX) that has been already executed on given Order in the entry
     */
    public BigDecimal getCumQuantity() {
        return getOrderQuantity().subtract(remainingQuantity);
    }

    /**
     * @return Average execution price. Returns BigDecimal.ZERO if nothing was executed.
     */
    public BigDecimal getAvgPrice() {
        BigDecimal cumQantity = getCumQuantity();
        if (cumQantity.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        } else {
            return MathUtils.divide(executedAmount, cumQantity);
        }
    }

    /**
     * @return timestamp, moment of time when this BookEntry was added into OrderBook
     */
    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("orderId", order.getOrderId())
            .add("instrumentId", order.getInstrumentId())
            .add("side", order.getSide())
            .add("type", order.getOrderType())
            .toString();
    }
}
