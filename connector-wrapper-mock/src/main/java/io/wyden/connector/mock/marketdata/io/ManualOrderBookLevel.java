package io.wyden.connector.mock.marketdata.io;

import ch.algotrader.api.connector.marketdata.domain.OrderBookLevelDTO;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class ManualOrderBookLevel extends OrderBookLevelDTO {

    @JsonCreator
    public ManualOrderBookLevel(
        @JsonProperty(value = "price") final BigDecimal price,
        @JsonProperty(value = "amount") final BigDecimal amount,
        @JsonProperty(value = "count") final Integer count) {
        super(price, amount, count);
    }
}
