@startuml
!pragma teoz true

title Pre-Trade Checks

actor User
participant "API" as API
participant "OG"
participant "Risk Engine" as Risk
participant PTC1
participant PTC2
participant "OC/SOR/BrokerDesks" as OEMS

User -> API: NewOrderSingle

API -> OG: ClientRequest
OG -> Risk: OemsRequest ptc=REQUIRED

alt approved
    Risk -> PTC1 ++
    & Risk -> PTC2 ++
    PTC1 --> Risk --: approved
    PTC2 --> Risk --: approved
    Risk -> OEMS: OemsRequest\nptc=APPROVED
else approved with warning
    Risk -> PTC1 ++
    & Risk -> PTC2 ++
    PTC1 --> Risk --: approved
    PTC2 --> Risk --: approved with warning
    Risk -> OEMS: OemsRequest\nptc=APPROVED_WITH_WARNING\n+ ptcWarning
else rejected
    Risk -> PTC1 ++
    & Risk -> PTC2 ++
    PTC1 -> Risk --: rejected
    Risk --> PTC2 !!: interrupt
    Risk -> OG: OemsResponse REJECTED\n+ reason
end


@enduml