package io.wyden.apiserver.rest.trading;

import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;

public final class ProtoTranslator {

    public static ClientOrderType translateOrderType(SharedModel.OrderType orderType) {
        return switch (orderType) {
            case STOP_LIMIT -> ClientOrderType.STOP_LIMIT;
            case STOP -> ClientOrderType.STOP;
            case LIMIT -> ClientOrderType.LIMIT;
            case MARKET -> ClientOrderType.MARKET;
            default -> throw new IllegalArgumentException("OrderType " + orderType + " is not supported yet");
        };
    }

    public static ClientTIF translateTif(SharedModel.TIF tif) {
        return switch (tif) {
            case DAY -> ClientTIF.DAY;
            case GTC -> ClientTIF.GTC;
            case GTD -> ClientTIF.GTD;
            case IOC -> ClientTIF.IOC;
            case FOK -> ClientTIF.FOK;
            default -> throw new IllegalArgumentException("TIF " + tif + " is not supported yet");
        };
    }

    public static String translateExpireTime(String expireTime) {
        return DateUtils.toFixUtcTime(DateUtils.epochMillisToZonedDateTime(expireTime));
    }

    public static ClientSide translateSide(SharedModel.Side side) {
        return switch (side) {
            case BUY -> ClientSide.BUY;
            case SELL -> ClientSide.SELL;
            case SELL_SHORT -> ClientSide.SELL_SHORT;
            case REDUCE_SHORT -> ClientSide.REDUCE_SHORT;
            default -> throw new IllegalArgumentException("Side " + side + " is not supported yet");
        };
    }
}
