package io.wyden.apiserver.rest.referencedata.instruments.mapper;

import io.wyden.apiserver.rest.referencedata.instruments.model.entity.BaseInstrumentEntity;
import io.wyden.apiserver.rest.referencedata.instruments.model.entity.ForexSpotPropertiesEntity;
import io.wyden.apiserver.rest.referencedata.instruments.model.entity.InstrumentEntity;
import io.wyden.apiserver.rest.referencedata.instruments.model.entity.InstrumentIdentifiersEntity;
import io.wyden.apiserver.rest.referencedata.instruments.model.entity.TradingConstraintsEntity;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.VenueType;
import io.wyden.published.referencedata.instrumentmodification.BaseInstrumentToModify;
import io.wyden.published.referencedata.instrumentmodification.ForexSpotPropertiesToModify;
import io.wyden.published.referencedata.instrumentmodification.InstrumentIdentifiersToModify;
import io.wyden.published.referencedata.instrumentmodification.InstrumentModificationCommandType;
import io.wyden.published.referencedata.instrumentmodification.InstrumentModificationRequest;
import io.wyden.published.referencedata.instrumentmodification.InstrumentToModify;
import io.wyden.published.referencedata.instrumentmodification.TradingConstraintsToModify;

import java.util.UUID;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.firstNonNull;
import static org.apache.commons.lang3.StringUtils.EMPTY;

//Class used for mapping between transit data model (InstrumentModificationRequest - proto) and service data model (InstrumentEntity - java)
public class InstrumentModificationMapper {

    public static InstrumentModificationRequest map(InstrumentModificationCommandType commandType, InstrumentEntity instrument, String clientId, String correlationObject) {
        return InstrumentModificationRequest.newBuilder()
            .setMessageId(UUID.randomUUID().toString())
            .setClientId(clientId)
            .setInstrumentModificationCommandType(commandType)
            .setInstrumentToModify(map(instrument))
            .setMetadata(Metadata.newBuilder()
                .setCorrelationObject(firstNonNull(correlationObject, EMPTY))
                .build())
            .build();
    }

    private static InstrumentToModify map(InstrumentEntity instrument) {
        InstrumentToModify.Builder builder = InstrumentToModify.newBuilder();
        if (nonNull(instrument.getBaseInstrument())) {
            builder.setBaseInstrument(buildBaseSecurityToModify(instrument.getBaseInstrument()));
        }
        if (nonNull(instrument.getInstrumentIdentifiers())) {
            builder.setInstrumentIdentifiers(buildInstrumentIdentifiersToModify(instrument.getInstrumentIdentifiers()));
        }
        if (nonNull(instrument.getTradingConstraints())) {
            builder.setTradingConstraints(buildTradingConstraintsToModify(instrument.getTradingConstraints()));
        }
        if (nonNull(instrument.getForexSpotProperties())) {
            builder.setForexSpotProperties(buildForexSpotPropertiesToModify(instrument.getForexSpotProperties()));
        }
        if (nonNull(instrument.getArchived())) {
            builder.setArchived(instrument.getArchived());
        }
        return builder.build();
    }

    private static BaseInstrumentToModify buildBaseSecurityToModify(BaseInstrumentEntity baseInstrument) {
        BaseInstrumentToModify.Builder builder = BaseInstrumentToModify.newBuilder();
        if (isNull(baseInstrument)) {
            return builder.build();
        }
        if (nonNull(baseInstrument.getVenueName())) {
            builder.setVenueName(baseInstrument.getVenueName());
        }
        if (nonNull(baseInstrument.getVenueType())) {
            builder.setVenueType(VenueType.valueOf(baseInstrument.getVenueType().name()));
        }
        if (nonNull(baseInstrument.getAssetClass())) {
            builder.setAssetClass(AssetClass.valueOf(baseInstrument.getAssetClass().toString()));
        }
        if (nonNull(baseInstrument.getDescription())) {
            builder.setDescription(baseInstrument.getDescription());
        }
        if (nonNull(baseInstrument.getQuoteCurrency())) {
            builder.setQuoteCurrency(baseInstrument.getQuoteCurrency());
        }
        if (nonNull(baseInstrument.getFeeCurrency())) {
            builder.setFeeCurrency(baseInstrument.getFeeCurrency());
        }
        if (nonNull(baseInstrument.getSettlementCurrency())) {
            builder.setSettlementCurrency(baseInstrument.getSettlementCurrency());
        }
        if (nonNull(baseInstrument.getInverseContract())) {
            builder.setInverseContract(baseInstrument.getInverseContract());
        }
        if (nonNull(baseInstrument.getSymbol())) {
            builder.setSymbol(baseInstrument.getSymbol());
        }
        return builder.build();
    }

    private static InstrumentIdentifiersToModify buildInstrumentIdentifiersToModify(InstrumentIdentifiersEntity instrumentIdentifiers) {
        InstrumentIdentifiersToModify.Builder builder = InstrumentIdentifiersToModify.newBuilder();
        if (isNull(instrumentIdentifiers)) {
            return builder.build();
        }
        if (nonNull(instrumentIdentifiers.getAdapterTicker())) {
            builder.setAdapterTicker(instrumentIdentifiers.getAdapterTicker());
        }
        if (nonNull(instrumentIdentifiers.getInstrumentId())) {
            builder.setInstrumentId(instrumentIdentifiers.getInstrumentId());
        }
        if (nonNull(instrumentIdentifiers.getTradingViewId())) {
            builder.setTradingViewId(instrumentIdentifiers.getTradingViewId());
        }
        if (nonNull(instrumentIdentifiers.getVenueTradingViewId())) {
            builder.setVenueTradingViewId(instrumentIdentifiers.getVenueTradingViewId());
        }
        return builder.build();
    }

    private static TradingConstraintsToModify buildTradingConstraintsToModify(TradingConstraintsEntity tradingConstraints) {
        TradingConstraintsToModify.Builder builder = TradingConstraintsToModify.newBuilder();
        if (isNull(tradingConstraints)) {
            return builder.build();
        }
        if (nonNull(tradingConstraints.getMinQty())) {
            builder.setMinQty(tradingConstraints.getMinQty());
        }
        if (nonNull(tradingConstraints.getMaxQty())) {
            builder.setMaxQty(tradingConstraints.getMaxQty());
        }
        if (nonNull(tradingConstraints.getMinQuoteQty())) {
            builder.setMinQuoteQty(tradingConstraints.getMinQuoteQty());
        }
        if (nonNull(tradingConstraints.getMaxQuoteQty())) {
            builder.setMaxQuoteQty(tradingConstraints.getMaxQuoteQty());
        }
        if (nonNull(tradingConstraints.getQtyIncr())) {
            builder.setQtyIncr(tradingConstraints.getQtyIncr());
        }
        if (nonNull(tradingConstraints.getQuoteQtyIncr())) {
            builder.setQuoteQtyIncr(tradingConstraints.getQuoteQtyIncr());
        }
        if (nonNull(tradingConstraints.getMinPrice())) {
            builder.setMinPrice(tradingConstraints.getMinPrice());
        }
        if (nonNull(tradingConstraints.getMaxPrice())) {
            builder.setMaxPrice(tradingConstraints.getMaxPrice());
        }
        if (nonNull(tradingConstraints.getPriceIncr())) {
            builder.setPriceIncr(tradingConstraints.getPriceIncr());
        }
        if (nonNull(tradingConstraints.getMinNotional())) {
            builder.setMinNotional(tradingConstraints.getMinNotional());
        }
        if (nonNull(tradingConstraints.getPriceScale())) {
            builder.setPriceScale(tradingConstraints.getPriceScale());
        }
        if (nonNull(tradingConstraints.getQtyScale())) {
            builder.setQtyScale(tradingConstraints.getQtyScale());
        }
        if (nonNull(tradingConstraints.getContractSize())) {
            builder.setContractSize(tradingConstraints.getContractSize());
        }
        if (nonNull(tradingConstraints.getTradeable())) {
            builder.setTradeable(tradingConstraints.getTradeable());
        }
        return builder.build();
    }

    private static ForexSpotPropertiesToModify buildForexSpotPropertiesToModify(ForexSpotPropertiesEntity forexSpotProperties) {
        ForexSpotPropertiesToModify.Builder builder = ForexSpotPropertiesToModify.newBuilder();
        if (isNull(forexSpotProperties)) {
            return builder.build();
        }
        if (nonNull(forexSpotProperties.getBaseCurrency())) {
            builder.setBaseCurrency(forexSpotProperties.getBaseCurrency());
        }
        return builder.build();
    }
}
