package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.apiserver.rest.orderhistory.OrderHistoryHttpClient;
import io.wyden.apiserver.rest.orderhistory.OrderHistoryService;
import io.wyden.apiserver.rest.orderhistory.model.CollectionPredicateInput;
import io.wyden.apiserver.rest.orderhistory.model.OrderHistorySearchInput;
import io.wyden.apiserver.rest.orderhistory.model.OrderStateResponse;
import io.wyden.apiserver.rest.orderhistory.model.OrderStateSearchInput;
import io.wyden.apiserver.rest.orderhistory.model.SimplePredicateInput;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.graphql.test.tester.GraphQlTester;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;


public class OrderStatesPermissionsTest extends PermissionsIntegrationTestNoDb {

    public static final ParameterizedTypeReference<PaginationModel.CursorConnection<OrderStateResponse>> CURSOR_CONNECTION_PTR = new ParameterizedTypeReference<>() {};

    @MockBean
    OrderHistoryHttpClient orderHistoryHttpClient;

    @MockBean
    private VenueAccountCacheFacade venueAccountCacheFacade;

    @MockBean
    private PortfoliosCacheFacade portfoliosCacheFacade;

    @Autowired
    private OrderHistoryService historyService;

    @BeforeEach
    void setUp() {
        Mockito.when(orderHistoryHttpClient.getOrderStateSnapshotsPaged(any(OrderHistorySearchInput.class))).thenAnswer(invocationOnMock -> {

            OrderHistorySearchInput orderHistorySearchInput = invocationOnMock.getArgument(0, OrderHistorySearchInput.class);
            Collection<SimplePredicateInput> simplePredicateInputs = orderHistorySearchInput.simplePredicates();
            Collection<CollectionPredicateInput> collectionPredicateInputs = orderHistorySearchInput.collectionPredicates();

            Set<String> portfolioIds = Stream.concat(
                    simplePredicateInputs.stream().filter(p -> p.field().equals(SimplePredicateInput.Field.PORTFOLIO_ID))
                        .map(SimplePredicateInput::value),
                    collectionPredicateInputs.stream().filter(p -> p.field().equals(CollectionPredicateInput.Field.PORTFOLIO_ID))
                        .map(CollectionPredicateInput::value)
                        .flatMap(Collection::stream))
                .collect(Collectors.toSet());

            Set<String> accountIds = Stream.concat(
                simplePredicateInputs.stream().filter(p -> p.field().equals(SimplePredicateInput.Field.VENUE_ACCOUNT_ID))
                    .map(SimplePredicateInput::value),
                collectionPredicateInputs.stream().filter(p -> p.field().equals(CollectionPredicateInput.Field.VENUE_ACCOUNT_ID))
                    .map(CollectionPredicateInput::value)
                    .flatMap(Collection::stream)).collect(Collectors.toSet());

            List<CursorEdge> cursorEdgeList = ORDER_STATES.values().stream()
                .filter(orderState -> {
                    if (!portfolioIds.isEmpty()) {
                        return portfolioIds.contains(orderState.getPortfolioId());
                    } else if (!accountIds.isEmpty()) {
                        return accountIds.contains(orderState.getVenueAccountId());
                    }
                    return true;
                })
                .map(orderState -> CursorEdge.newBuilder().setCursor(orderState.getUpdatedAt()).setNode(CursorNode.newBuilder().setOrderState(orderState).build()).build())
                .toList();

            return Mono.just(CursorConnection.newBuilder()
                .setPageInfo(PageInfo.newBuilder().setHasNextPage(false).build())
                .addAllEdges(cursorEdgeList).build());
        });

        Mockito.when(venueAccountCacheFacade.venueAccountDetails()).thenReturn(List.of(
            VenueAccount.newBuilder().setId("allowedTarget").build()
        ));
    }

    @Test
    @WithMockCustomUser
    void whenPermittedToVenueAccountOnlyShouldSeeVenueAccountOrders() {
        // arrange
        grant("venue.account", "read", "allowedTarget");
        grant("portfolio", "read");
        // act
        GraphQlTester.Path response = executeFile("order-states-with-predicates",
            Map.of("search", new OrderStateSearchInput(
                Set.of(),
                Set.of(),
                Set.of(),
                SharedModel.SortingOrder.DESC,
                null,
                null
            )))
            .path("orderStatesWithPredicates");

        // assert
        numberOfEdges(response, 2);
        orderIds(response, "1", "2");
        hasNextPage(response, false);
    }

    @Test
    @WithMockCustomUser
    void whenNotPermittedToVenueAccountAccessShouldSeeNoOrders() {
        // arrange
        grant("venue.account", "read", "nonExistingTarget");
        ORDER_STATES.values().forEach(historyService::accept);

        // act
        GraphQlTester.Path response = executeFile("order-states-with-predicates",
            Map.of("search", new OrderStateSearchInput(
                Set.of(),
                Set.of(),
                Set.of(),
                SharedModel.SortingOrder.DESC,
                null,
                null
            )))
            .path("orderStatesWithPredicates");

        // assert
        numberOfEdges(response, 0);
        hasNextPage(response, false);
    }

    private static void hasNextPage(GraphQlTester.Path response, boolean expected) {
        assertThat(response.entity(CURSOR_CONNECTION_PTR).get()
            .pageInfo().hasNextPage())
            .isEqualTo(expected);
    }

    private static void numberOfEdges(GraphQlTester.Path response, int expected) {
        assertThat(response.entity(CURSOR_CONNECTION_PTR).get()
            .edges())
            .hasSize(expected);
    }

    private static void orderIds(GraphQlTester.Path response, String... expected) {
        assertThat(response.entity(CURSOR_CONNECTION_PTR).get()
            .edges().stream().map(edge -> edge.node().getOrderId()).toList())
            .containsExactlyInAnyOrder(expected);
    }
}
