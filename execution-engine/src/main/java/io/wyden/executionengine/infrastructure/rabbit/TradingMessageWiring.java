package io.wyden.executionengine.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TradingMessageWiring {

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations rabbitDestinations;
    private final String queueName;

    public TradingMessageWiring(RabbitIntegrator rabbitIntegrator,
                                RabbitDestinations rabbitDestinations,
                                @Value("${rabbitmq.trading-execution-engine-queue}") String queueName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitDestinations = rabbitDestinations;
        this.queueName = queueName;
    }

    @Bean
    public RabbitQueue<Message> tradingMessageQueue() {
        return declareQueue();
    }

    private RabbitQueue<Message> declareQueue() {
        RabbitExchange<Message> dlx = rabbitDestinations.getTradingDLX();

        return new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setSingleActiveConsumer(true)
            .setDeadLetterExchange(dlx)
            .declare();
    }
}
