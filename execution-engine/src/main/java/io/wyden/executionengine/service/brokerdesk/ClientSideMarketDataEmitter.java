package io.wyden.executionengine.service.brokerdesk;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.executionengine.domain.ClientSideInstrument;
import io.wyden.executionengine.service.price.model.MarketDataEventType;
import io.wyden.executionengine.service.price.model.MarketDataSubscriptionKey;
import io.wyden.executionengine.service.utils.ProtobufUtils;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.Disposable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static io.wyden.executionengine.service.price.model.MarketDataSubscriptionKey.asKey;

public class ClientSideMarketDataEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientSideMarketDataEmitter.class);

    private final ClientSideInstrument clientSideInstrument;
    private final BrokerDeskQuotingService quotingService;
    private final RabbitExchange<MarketDataEvent> marketDataExchange;
    private final String brokerDeskId;
    private final int mdRequestCleanupInterval;
    private final MeterRegistry meterRegistry;
    private final Map<MarketDataSubscriptionKey, List<MarketDataRequest>> clientSideSubscriptions = new HashMap<>();
    private final ScheduledExecutorService executorService;
    private final AtomicReference<Disposable> quoteSubscription;

    public ClientSideMarketDataEmitter(ClientSideInstrument clientSideInstrument,
                                       BrokerDeskQuotingService brokerDeskQuotingService,
                                       RabbitExchange<MarketDataEvent> marketDataExchange,
                                       String brokerDeskId,
                                       Telemetry telemetry,
                                       int mdRequestCleanupInterval
    ) {
        this.meterRegistry = telemetry.getMeterRegistry();
        this.clientSideInstrument = clientSideInstrument;
        this.quotingService = brokerDeskQuotingService;
        this.marketDataExchange = marketDataExchange;
        this.brokerDeskId = brokerDeskId;
        this.mdRequestCleanupInterval = mdRequestCleanupInterval;

        this.executorService = Executors.newScheduledThreadPool(1);
        this.meterRegistry.gaugeMapSize("wyden.market-data.subscriptions", List.of(), clientSideSubscriptions);

        this.quoteSubscription = new AtomicReference<>(startProducer());

        startCleaner();
    }

    private Disposable startProducer() {
        return quotingService.getMarketDataFlux()
            .filter(this::externalDemandExists)
            .subscribe(this::emit);
    }

    private void startCleaner() {
        LOGGER.info("Starting market data subscriptions cleanup with interval of {} seconds", mdRequestCleanupInterval);
        try {
            executorService.scheduleWithFixedDelay(clientSideMarketDataSubscriptionCleaner(), 1, mdRequestCleanupInterval, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("Error when trying to start cleanup heart beating", e);
        }
    }

    /**
     * @return function that will iterate over all currently running Client Side subscriptions and:
     * <ul>
     *     <li>for every non-empty subscription set - clear it</li>
     *     <li>if already cleared - remove completely</li>
     * </ul>
     * That way, we rely on the client to renew the subscription cyclically.
     * If subscription has not been removed in time, and there's no other demand for given MarketDataSubscriptionKey,
     * then we will stop emitting market data events for this key to the RabbitMQ exchange.
     */
    private Runnable clientSideMarketDataSubscriptionCleaner() {
        return () -> clientSideSubscriptions.forEach((key, requests) -> {
            if (requests.isEmpty()) {
                clientSideSubscriptions.remove(key);
            } else {
                requests.clear();
            }
        });
    }

    void stop() {
        LOGGER.info("Stopping: {}", this);
        Disposable old = quoteSubscription.getAndSet(null);

        if (old != null) {
            old.dispose();
            LOGGER.info("Stopped: {}", this);
        } else {
            LOGGER.debug("Already stopped, stop request won't take effect: {}", this);
        }
    }

    void resume() {
        if(Objects.isNull(quoteSubscription.get())) {
            quoteSubscription.set(startProducer());
            LOGGER.info("Client side market data emitter resumed: {}", this);
        } else {
            LOGGER.debug("Client side market data emitter already active, ignoring resume request: {}", this);
        }
    }

    void onMarketDataRequest(MarketDataRequest marketDataRequest) {
        MarketDataSubscriptionKey marketDataSubscriptionKey = new MarketDataSubscriptionKey(marketDataRequest);
        boolean valid = validate(marketDataRequest);
        updateSubscriptionMetrics(marketDataRequest.getInstrumentKey().getInstrumentId(), marketDataRequest.getMarketDepth() == 1 ? "L1" : "L2" );
        if (valid) {
            executorService.execute(() -> clientSideSubscriptions.computeIfAbsent(marketDataSubscriptionKey, k -> new ArrayList<>()).add(marketDataRequest));
        }
    }

    private boolean externalDemandExists(MarketDataEvent marketDataEvent) {
        MarketDataSubscriptionKey key = asKey(marketDataEvent);

        if (key == null) {
            return false;
        }

        return clientSideSubscriptions.containsKey(key);
    }

    private boolean validate(MarketDataRequest marketDataRequest) {
        if (!clientSideInstrument.getInstrumentId().equals(marketDataRequest.getInstrumentKey().getInstrumentId())) {
            // TODO - emit MarketDataReject https://algotrader.atlassian.net/browse/AC-1620

            LOGGER.warn("Market data request invalid - got {} for {}", marketDataRequest.getInstrumentKey().getInstrumentId(), clientSideInstrument.getInstrumentId());
            return false;
        }

        if (!brokerDeskId.equals(marketDataRequest.getInstrumentKey().getVenueAccount())) {
            // TODO - emit MarketDataReject https://algotrader.atlassian.net/browse/AC-1620

            LOGGER.warn("Market data request invalid - got {} for {}", marketDataRequest.getInstrumentKey().getInstrumentId(), clientSideInstrument.getInstrumentId());
            return false;
        }

        if (quoteSubscription.get() == null) {
            // TODO - emit MarketDataReject https://algotrader.atlassian.net/browse/AC-1620

            LOGGER.warn("Market data request invalid - got {} for disabled {}", marketDataRequest.getInstrumentKey().getInstrumentId(), clientSideInstrument.getInstrumentId());
            return false;
        }

        return true;
    }

    private void emit(MarketDataEvent message) {
        MarketDataEventType marketDataEventType = MarketDataEventType.from(message);
        String depthString;
        if (Objects.nonNull(marketDataEventType)) {
            depthString = marketDataEventType.name();
        } else {
            LOGGER.warn("Missing MarketDataEventType in message: {}. Market data will not be emitted", ProtobufUtils.shortDebugString(message));
            return;
        }

        try {
            Map<String, String> headers = Map.of(
                OemsHeader.VENUE_ACCOUNT.getHeaderName(), brokerDeskId,
                OemsHeader.INSTRUMENT_ID.getHeaderName(), clientSideInstrument.getInstrumentId(),
                OemsHeader.MD_LEVEL.getHeaderName(), depthString
            );

            updateEventMetrics(clientSideInstrument.getInstrumentId(), depthString);

            LOGGER.trace("Market data is emitted to " + marketDataExchange.getName() + " exchange with headers=" + headers);
            marketDataExchange.tryPublishWithHeaders(message, brokerDeskId, headers);
        } catch (Exception ex) {
            LOGGER.error("Failed to emit market data", ex);
        }
    }

    private void updateEventMetrics(String instrumentId, String depth) {
        try {
            this.meterRegistry
                .counter("wyden.market-data.events-outgoing", Tags.of("instrumentId", instrumentId, "depth", depth, "source", "Execution-Engine"))
                .increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private void updateSubscriptionMetrics(String symbol, String depth) {
        try {
            this.meterRegistry
                .counter("wyden.market-data.subscribe-incoming", Tags.of("instrumentId", symbol, "depth", depth))
                .increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
    @Override
    public String toString() {
        return "ClientSideMarketDataProducer{" +
            "clientSideInstrument=" + clientSideInstrument +
            ", venueAccount='" + brokerDeskId + '\'' +
            ", marketDataExchange=" + marketDataExchange +
            ", mdRequestCleanupInterval=" + mdRequestCleanupInterval +
            ", clientSideSubscriptions=" + clientSideSubscriptions +
            '}';
    }
}
