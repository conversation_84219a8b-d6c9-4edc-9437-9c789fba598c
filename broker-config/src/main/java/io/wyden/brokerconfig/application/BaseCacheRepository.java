package io.wyden.brokerconfig.application;

import io.wyden.brokerconfig.domain.BaseRepository;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

public abstract class BaseCacheRepository<T> implements BaseRepository<String, T> {

    private final Map<String, T> cache;
    private final Function<T, String> keyExtractor;

    public BaseCacheRepository(Map<String, T> cache, Function<T, String> keyExtractor) {
        this.cache = cache;
        this.keyExtractor = keyExtractor;
    }

    @Override
    public Optional<T> find(String key) {
        T elem = cache.get(key);
        return Optional.ofNullable(elem);
    }

    @Override
    public Collection<T> findAll() {
        return cache.values();
    }

    @Override
    public Collection<String> findAllKeys() {
        return cache.keySet();
    }

    @Override
    public void save(T elem) {
        cache.put(keyExtractor.apply(elem), elem);
    }

    @Override
    public boolean delete(T elem) {
        T deleted = cache.remove(keyExtractor.apply(elem));
        return deleted != null;
    }
}
