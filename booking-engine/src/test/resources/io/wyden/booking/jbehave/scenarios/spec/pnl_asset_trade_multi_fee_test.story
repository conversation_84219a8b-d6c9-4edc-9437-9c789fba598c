Meta:

Scenario: deposit USD
Given market data prices:
| instrument | price      |
| BTC        | 10_000 USD |
| BTC/USD    | 10_000 USD |
| ETH        | 1_200 USD  |
| ETH        | 0.12 BTC   |

When Client 1 deposits 20_100 USD into Bitfinex account
Then position net state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_100 | 20_100 | 20_100      | -           | -             | 1            |
| Client 1    | BTC/USD    | -      | -      | -           | -           | -             | -            |
| Client 1    | ETH        | -      | -      | -           | -           | -             | -            |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | BTC/USD    | -   | -    | -           | -           | -             | -            |

And all Client 1 aggregated positions state changes to:
| cost   | marketValue | realizedPnL | unrealizedPnL |
| 20_100 | 20_100      | -           | -             |


Scenario: deposit ETH
Given market data prices:
| instrument | price      |
| BTC        | 10_000 USD |
| BTC/USD    | 10_000 USD |
| ETH        | 1_205 USD  |
| ETH        | 0.1205 BTC |

When Client 1 deposits 1 ETH into Bitfinex account
Then position net state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_100 | 20_100 | 20_100      | -           | -             | 1            |
| Client 1    | BTC/USD    | -      | -      | -           | -           | -             | -            |
| Client 1    | ETH        | 1      | 1_205  | 1_205       | -           | -             | 1_205        |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | BTC/USD    | -   | -    | -           | -           | -             | -            |

And all Client 1 aggregated positions state changes to:
| cost   | marketValue | realizedPnL | unrealizedPnL |
| 21_305 | 21_305      | -           | -             |


Scenario: buy BTC/USD
Given market data prices:
| instrument | price      |
| BTC        | 10_500 USD |
| BTC/USD    | 10_500 USD |
| ETH        | 1_210 USD  |
| ETH        | 0.1205 BTC |

When Client 1 trades 1 contract of BTC/USD with 0.01 ETH transaction fee and 50 USD exchange fee
Then position net state change to:
| portfolioId | instrument | qty    | cost     | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_050 | 20_050   | 20_050      | -           | -             | 1            |
| Client 1    | BTC/USD    | 1      | 10_562.1 | 10_500      | -           | -62.1         | 10_562.1     |
| Client 1    | ETH        | 0.99   | 1_192.95 | 1_197.9     | 0.05        | 4.95          | 1_205        |

And position gross state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | BTC/USD    | 1   | 10_500 | 10_500      | -           | -             | 10_500       |

And all Client 1 aggregated positions state changes to:
| cost      | marketValue | realizedPnL | unrealizedPnL |
| 31_805.05 | 31_747.90   | 0.05        | -57.15        |


Scenario: sell BTC/USD
Given market data prices:
| instrument | price      |
| BTC        | 11_000 USD |
| BTC/USD    | 11_000 USD |
| ETH        | 1_215 USD  |
| ETH        | 0.1105 BTC |

When Client 1 trades -1 contract of BTC/USD with 0.01 ETH transaction fee and 50 USD exchange fee
Then position net state change to:
| portfolioId | instrument | qty    | cost     | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_500 | 20_500   | 20_500      | -           | -             | 1            |
| Client 1    | BTC/USD    | -      | -        | -           | 375.75      | -             | -            |
| Client 1    | ETH        | 0.98   | 1_180.90 | 1_190.70    | 0.15        | 9.80          | 1_205        |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | BTC/USD    | -   | -    | -           | 500         | -             | -            |

And all Client 1 aggregated positions state changes to:
| cost      | marketValue | realizedPnL | unrealizedPnL |
| 21_680.90 | 21_690.70   | 375.90      | 9.80          |


Scenario: withdraw ETH
Given market data prices:
| instrument | price      |
| BTC        | 11_500 USD |
| BTC/USD    | 11_500 USD |
| ETH        | 1_220 USD  |
| ETH        | 0.1105 BTC |

When Client 1 withdraws all remaining ETH
Then position net state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_500 | 20_500 | 20_500      | -           | -             | 1            |
| Client 1    | BTC/USD    | -      | -      | -           | 375.75      | -             | -            |
| Client 1    | ETH        | -      | -      | -           | 14.85       | -             | -            |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | BTC/USD    | -   | -    | -           | 500         | -             | -            |

And all Client 1 aggregated positions state changes to:
| cost   | marketValue | realizedPnL | unrealizedPnL |
| 20_500 | 20_500      | 390.60      | -             |


Scenario: withdraw USD
Given market data prices:
| instrument | price      |
| BTC        | 11_500 USD |
| BTC/USD    | 11_500 USD |
| ETH        | 1_225 USD  |
| ETH        | 0.1065 BTC |

When Client 1 withdraws all remaining USD
Then position net state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | -   | -    | -           | -           | -             | -            |
| Client 1    | BTC/USD    | -   | -    | -           | 375.75      | -             | -            |
| Client 1    | ETH        | -   | -    | -           | 14.85       | -             | -            |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | BTC/USD    | -   | -    | -           | 500         | -             | -            |

And all Client 1 aggregated positions state changes to:
| cost | marketValue | realizedPnL | unrealizedPnL |
| -    | -           | 390.60      | -             |
