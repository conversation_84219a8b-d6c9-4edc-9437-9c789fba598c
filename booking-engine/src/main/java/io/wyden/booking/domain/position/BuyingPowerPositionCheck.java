package io.wyden.booking.domain.position;

import io.wyden.booking.application.state.StateOutput;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.ledgerentry.RawLedgerEntry;
import io.wyden.booking.domain.ledgerentry.SimpleReference;
import io.wyden.booking.domain.reservation.Reservation;
import io.wyden.booking.domain.reservation.ReservationFee;
import io.wyden.booking.domain.reservation.payment.WithdrawalReservation;
import io.wyden.booking.domain.reservation.trade.ClientCashTradeReservation;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static io.wyden.cloudutils.tools.BigDecimalUtils.isNegative;

@Component
public class BuyingPowerPositionCheck implements PositionCheck {

    @Override
    public void check(Reservation reservation, StateOutput stateOutput) {
        if (reservation instanceof ClientCashTradeReservation tradeReservation) {
            Stream<Currency> reservationCurrencies = Stream.of(tradeReservation.getBaseCurrency(), tradeReservation.getCurrency());
            Stream<Currency> feeCurrencies = tradeReservation.getFees().stream().map(ReservationFee::getCurrency);

            Set<Currency> affectedCurrencies = Stream.concat(reservationCurrencies, feeCurrencies)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            PortfolioReference clientPortfolio = tradeReservation.getPortfolio();

            // make sure to check available funds for all currencies including fees
            affectedCurrencies.forEach(currency -> verifyAvailableForTradingAmount(clientPortfolio, currency, tradeReservation, stateOutput));
        }

        if (reservation instanceof WithdrawalReservation withdrawalReservation) {
            Stream<Currency> reservationCurrencies = Stream.of(withdrawalReservation.getCurrency());
            Stream<Currency> feeCurrencies = withdrawalReservation.getFees().stream().map(ReservationFee::getCurrency);

            Set<Currency> affectedCurrencies = Stream.concat(reservationCurrencies, feeCurrencies)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            PortfolioReference clientPortfolio = withdrawalReservation.getPortfolio();

            // make sure to check available funds for all currencies including fees
            affectedCurrencies.forEach(currency -> verifyAvailableForWithdrawalAmount(clientPortfolio, currency, withdrawalReservation, stateOutput));
        }
    }

    public void verifyAvailableForTradingAmount(PortfolioReference portfolioReference, Currency currency, Reservation reservation, StateOutput stateOutput) {
        stateOutput.findPositionByReference(SimpleReference.of(portfolioReference, currency))
            .ifPresent(position -> {
                BigDecimal availableForTradingQuantity = position.getAvailableForTradingQuantity();
                if (isDecreasingPosition(reservation, position) && isNegative(availableForTradingQuantity)) {
                    throw new IllegalArgumentException("Insufficient funds for trading after reservation (%s %s): %s. Reservation will be rolled-back."
                        .formatted(availableForTradingQuantity, currency, reservation));
                }
            });
    }

    public void verifyAvailableForWithdrawalAmount(PortfolioReference portfolioReference, Currency currency, Reservation reservation, StateOutput stateOutput) {
        stateOutput.findPositionByReference(SimpleReference.of(portfolioReference, currency))
            .ifPresent(position -> {
                BigDecimal availableForWithdrawalQuantity = position.getAvailableForWithdrawalQuantity();
                if (isDecreasingPosition(reservation, position) && isNegative(availableForWithdrawalQuantity)) {
                    throw new IllegalArgumentException("Insufficient funds for withdrawal after reservation (%s %s): %s. Reservation will be rolled-back."
                        .formatted(availableForWithdrawalQuantity, currency, reservation));
                }
            });
    }

    private boolean isDecreasingPosition(Reservation reservation, Position position) {
        BigDecimal delta = reservation.getLedgerEntries().stream()
            .filter(rle -> rle.getInstrument().getCurrency().equals(position.getInstrumentCurrency()) && rle.getReference().getReferenceId().equals(position.getReference().getReferenceId()))
            .map(RawLedgerEntry::getQuantity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return isNegative(delta);
    }
}
