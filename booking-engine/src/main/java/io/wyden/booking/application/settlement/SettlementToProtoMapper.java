package io.wyden.booking.application.settlement;

import io.wyden.booking.domain.settlement.SettlementModel;
import io.wyden.published.booking.settlement.MismatchedSettlementLeg;
import io.wyden.published.booking.settlement.SettlementLeg;

import java.util.Collection;
import java.util.List;

import static io.wyden.booking.utils.CollectionUtils.isEmpty;
import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;

public final class SettlementToProtoMapper {

    private SettlementToProtoMapper() {
    }

    public static Collection<SettlementLeg> map(Collection<SettlementModel.SettlementLeg> legs) {
        if (isEmpty(legs)) {
            return List.of();
        }

        return legs.stream()
            .map(leg -> SettlementLeg.newBuilder()
                .setSourceId(leg.legId().sourceId())
                .setTargetId(leg.legId().targetId())
                .setAsset(leg.legId().asset())
                .setQuantity(toProtoString(leg.quantity()))
                .build())
            .toList();
    }

    public static Collection<MismatchedSettlementLeg> mapMismatchedLegs(Collection<SettlementModel.MismatchedSettlementLeg> legs) {
        if (isEmpty(legs)) {
            return List.of();
        }

        return legs.stream()
            .map(leg -> MismatchedSettlementLeg.newBuilder()
                .setSourceId(leg.legId().sourceId())
                .setTargetId(leg.legId().targetId())
                .setAsset(leg.legId().asset())
                .setRequestedQuantity(toProtoString(leg.requestedQuantity()))
                .setCalculatedQuantity(toProtoString(leg.calculatedQuantity()))
                .build())
            .toList();
    }
}
