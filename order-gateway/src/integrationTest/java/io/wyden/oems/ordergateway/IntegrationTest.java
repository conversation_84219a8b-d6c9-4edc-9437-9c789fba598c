package io.wyden.oems.ordergateway;

import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.DirtiesContext;

import static io.wyden.published.client.ClientOrderType.LIMIT;
import static io.wyden.published.client.ClientOrderType.MARKET;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.springframework.test.annotation.DirtiesContext.ClassMode.AFTER_CLASS;

@DirtiesContext(classMode = AFTER_CLASS)
public class IntegrationTest extends GatewayIntegrationTestBase {

    @Test
    void incomingExecutableNewOrderSingleShouldBeExecutedOnVenue() {
        ClientRequest clientOrder = client.requestsNewOrder(MARKET);
        OemsRequest requestInCollider = collider.awaitNewOrderRequest();
        assertThat(requestInCollider.getRootOrderId()).isEqualTo(requestInCollider.getOrderId());
        venueOrderId = requestInCollider.getOrderId();
        verifyClientRequestAndConnectorRequestContentIsTheSame(clientOrder, requestInCollider);

        String orderStatusRequestId = client.requestsOrderStatus(clientOrder);
        ClientResponse executionReport = client.awaitExecutionReportPendingNew();
        orderId = executionReport.getOrderId();
        verifyExecutionReportPendingNew(1, orderStatusRequestId, executionReport);

        OemsResponse emittedByCollider = collider.emitExecutionReportNew(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportNew(2, orderId, null, emittedByCollider));

        OemsResponse fillEmittedByCollider = collider.emitExecutionReportFilled(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportFilledInOneFill(3, orderId, venueAccount, fillEmittedByCollider));
    }

    @Test
    void duplicatedNewOrderRequestShouldBeIdempotent() {
        ClientRequest clientOrder = client.requestsNewOrderTwice(MARKET);
        OemsRequest requestInCollider = collider.awaitNewOrderRequest();
        venueOrderId = requestInCollider.getOrderId();
        verifyClientRequestAndConnectorRequestContentIsTheSame(clientOrder, requestInCollider);

        String orderStatusRequestId = client.requestsOrderStatus(clientOrder);
        ClientResponse executionReport = client.awaitExecutionReportPendingNew();
        orderId = executionReport.getOrderId();
        verifyExecutionReportPendingNew(1, orderStatusRequestId, executionReport);

        OemsResponse emittedByCollider = collider.emitExecutionReportNew(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportNew(2, orderId, null, emittedByCollider));

        OemsResponse emittedExecutionReportFilled = collider.emitExecutionReportFilled(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportFilledInOneFill(3, orderId, venueAccount, emittedExecutionReportFilled));
    }

    @Test
    void incomingCancelShouldBeCancelled() {
        ClientRequest clientOrder = limitOrderIsRequestedAndAcceptedAsNew();

        String cancelRequestId = client.requestsCancel(clientOrder);

        client.awaitClientResponse(testingData.expectedExecutionReportPendingCancel(4, orderId, cancelRequestId));

        collider.awaitCancelRequest();

        OemsResponse emittedByCollider = collider.emitExecutionReportCancelled(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportCanceled(5, orderId, cancelRequestId, emittedByCollider));
    }

    @Test
    void cancelRejectDoNotAffectState() {
        ClientRequest clientOrder = newOrderIsRequestedAndChildIsForwaredToCollider();

        OemsResponse emittedByCollider = collider.emitExecutionReportNew(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportNew(2, orderId, null, emittedByCollider));

        String cancelRequestId = client.requestsCancel(clientOrder);

        client.awaitClientResponse(testingData.expectedExecutionReportPendingCancel(4, orderId, cancelRequestId));

        collider.awaitCancelRequest();

        collider.emitCancelReject(venueOrderId, OemsOrderStatus.STATUS_NEW);

        client.awaitCancelReject();

        String orderStatusRequestId2 = client.requestsOrderStatus(clientOrder);

        client.awaitClientResponse(testingData.expectedExecutionReportNew(5, orderId, orderStatusRequestId2, null));
    }

    @Test
    void calculatedReportShouldBePropagated() {
        ClientRequest clientOrder = client.requestsNewOrder(MARKET);
        OemsRequest requestInCollider = collider.awaitNewOrderRequest();
        assertThat(requestInCollider.getRootOrderId()).isEqualTo(requestInCollider.getOrderId());
        venueOrderId = requestInCollider.getOrderId();
        verifyClientRequestAndConnectorRequestContentIsTheSame(clientOrder, requestInCollider);

        String orderStatusRequestId = client.requestsOrderStatus(clientOrder);
        ClientResponse executionReport = client.awaitExecutionReportPendingNew();
        orderId = executionReport.getOrderId();
        verifyExecutionReportPendingNew(1, orderStatusRequestId, executionReport);

        OemsResponse emittedByCollider = collider.emitExecutionReportNew(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportNew(2, orderId, null, emittedByCollider));

        OemsResponse calculatedEmittedByCollider = collider.emitExecutionReportCalculatedAfterNew(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportCalculatedAfterNew(3, orderId, calculatedEmittedByCollider));

        OemsResponse fillEmittedByCollider = collider.emitExecutionReportFilled(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportFilledInOneFill(4, orderId, venueAccount, fillEmittedByCollider));
    }

    @Test
    void givenPendingCancelReplace_whenPartialFilled_thenEmit() {
        // submit
        ClientRequest clientOrder = client.requestsNewOrder(LIMIT);

        // request status to get Wyden's order id
        String orderStatusRequestId = client.requestsOrderStatus(clientOrder);
        ClientResponse executionReport = client.awaitExecutionReportPendingNew();
        orderId = executionReport.getOrderId();

        // Assign NEW by collider, propagate to Client
        OemsRequest requestInCollider = collider.awaitNewOrderRequest();
        assertThat(requestInCollider.getRootOrderId()).isEqualTo(requestInCollider.getOrderId());
        venueOrderId = requestInCollider.getOrderId();
        verifyClientRequestAndConnectorRequestContentIsTheSame(clientOrder, requestInCollider);
        OemsResponse emittedByCollider = collider.emitExecutionReportNew(venueOrderId);
        client.awaitClientResponse(testingData.expectedExecutionReportNew(2, orderId, null, emittedByCollider));

        // submit CANCEL_REPLACE
        String cancelReplaceRequestId = client.requestsCancelReplace(clientOrder);

        // receive PENDING_REPLACE
        client.awaitClientResponse(testingData.expectedExecutionReportPendingCancelReplace(4, orderId, cancelReplaceRequestId, clientOrder.getClOrderId(), ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, null));

        // Collider PARTIAL_FILLs
        OemsResponse fillEmittedByCollider = collider.emitExecutionReportPartiallyFilled(venueOrderId);

        // Client receives ExecType Partial Fill, Status Pending Cancel
        client.awaitClientResponse(testingData.expectedExecutionReportPendingCancelReplace(5, orderId, clientOrder.getClOrderId(), "", ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, fillEmittedByCollider));
    }

    private ClientRequest limitOrderIsRequestedAndAcceptedAsNew() {
        ClientRequest clientOrder = newOrderIsRequestedAndChildIsForwaredToCollider();

        OemsResponse emittedByCollider = collider.emitExecutionReportNew(venueOrderId);

        client.awaitClientResponse(testingData.expectedExecutionReportNew(2, orderId, null, emittedByCollider));
        return clientOrder;
    }

    private ClientRequest newOrderIsRequestedAndChildIsForwaredToCollider() {
        ClientRequest clientOrder = client.requestsNewOrder(LIMIT);
        OemsRequest requestInCollider = collider.awaitNewOrderRequest();

        String orderStatusRequestId = client.requestsOrderStatus(clientOrder);
        ClientResponse executionReport = client.awaitExecutionReportPendingNew();
        orderId = executionReport.getOrderId();
        verifyExecutionReportPendingNew(1, orderStatusRequestId, executionReport);

        venueOrderId = requestInCollider.getOrderId();
        return clientOrder;
    }

    private void verifyExecutionReportPendingNew(int sequenceNumber, String reqId, ClientResponse executionReport) {
        assertThat(executionReport)
            .as("Gateway should register and share Order status as PENDING_NEW")
            .usingComparator(ignoreTimestampComparator())
            .isEqualTo(testingData.expectedExecutionReportPendingNew(
                sequenceNumber,
                reqId,
                orderId,
                executionReport.getMetadata().getResponseId(),
                executionReport.getMetadata().getCreatedAt()
            ));
    }

    private void verifyClientRequestAndConnectorRequestContentIsTheSame(ClientRequest clientOrder, OemsRequest requestInCollider) {
        ClientOrderType clientOrderType = clientOrder.getOrderType();
        String clientOrderPrice = clientOrder.getPrice();
        String clientOrderQuantity = clientOrder.getQuantity();
        ClientSide clientOrderSide = clientOrder.getSide();
        String clientOrderStopPrice = clientOrder.getStopPrice();
        ClientTIF clientOrderTif = clientOrder.getTif();

        assertThat(requestInCollider.getOrderType().toString()).isEqualTo(clientOrderType.toString());
        assertThat(requestInCollider.getPrice()).isEqualTo(clientOrderPrice);
        assertThat(requestInCollider.getQuantity()).isEqualTo(clientOrderQuantity);
        assertThat(requestInCollider.getSide().toString()).isEqualTo(clientOrderSide.toString());
        assertThat(requestInCollider.getStopPrice()).isEqualTo(clientOrderStopPrice);
        assertThat(requestInCollider.getTif().toString()).isEqualTo(clientOrderTif.toString());
        assertThat(requestInCollider.getCurrency()).isEqualTo(clientOrder.getCurrency());
        assertThat(requestInCollider.getAvailableBalanceAmount()).isEqualTo(clientOrder.getAvailableBalanceAmount());
        assertThat(requestInCollider.getAvailableBalanceCurrency()).isEqualTo(clientOrder.getAvailableBalanceCurrency());

        assertThat(requestInCollider.getRootOrderId()).isEqualTo(clientOrder.getOrderId());
        assertThat(requestInCollider.getClientRootOrderId()).isEqualTo(clientOrder.getClOrderId());
    }
}
