package io.wyden.sor.service.connectorstate;

import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.targetregistry.ConnectorCapabilities;
import io.wyden.published.targetregistry.ConnectorOrderType;
import io.wyden.published.targetregistry.ConnectorTIF;
import io.wyden.published.targetregistry.DefaultTIFs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class TIFResolver {
    private static final Logger LOGGER = LoggerFactory.getLogger(TIFResolver.class);

    private final ConnectorStateService connectorStateService;

    public TIFResolver(ConnectorStateService connectorStateService) {
        this.connectorStateService = connectorStateService;
    }

    public Optional<OemsTIF> resolve(OemsRequest smartOrder, String venueAccountName) {
        return connectorStateService.getTradingCapabilities(venueAccountName)
            .flatMap(tradingCapabilities -> doResolve(venueAccountName, tradingCapabilities, smartOrder));
    }

    private Optional<OemsTIF> doResolve(String venueAccountName, ConnectorCapabilities.TradingCapabilities tradingCapabilities, OemsRequest request) {
        OemsOrderType orderType = request.getOrderType();
        OemsTIF requestedTIF = request.getTif();

        Set<OemsTIF> supportedTIFs = tradingCapabilities.getSupportedTifs().getSupportedTifsList().stream()
            .filter(supportedTIF -> map(supportedTIF.getOrderType()) == orderType)
            .flatMap(supportedTIF -> supportedTIF.getTifsList().stream())
            .map(TIFResolver::map)
            .collect(Collectors.toSet());

        if (supportedTIFs.contains(requestedTIF)) {
            LOGGER.debug("Resolved TIF {} as it's supported for order type {} and venue account {}", requestedTIF, orderType, venueAccountName);
            return Optional.of(requestedTIF);
        } else {
            ConnectorTIF defaultConnectorTIF = getDefaultTif(tradingCapabilities.getDefaultTifs(), orderType);
                //defaultTifFunction(orderType).apply(tradingCapabilities.getDefaultTifs());

            if (defaultConnectorTIF == ConnectorTIF.TIF_UNSPECIFIED) {
                LOGGER.debug("TIF {} not supported and default TIF not specified for order type {} and venue account {}", requestedTIF, orderType, venueAccountName);
                return Optional.empty();
            }

            if (defaultConnectorTIF == ConnectorTIF.GTD) {
                LOGGER.debug("TIF {} not supported and default TIF for order type {} and venue account {} = GTD. Unable to determine expire time for the order", requestedTIF, orderType, venueAccountName);
                return Optional.empty();
            }

            OemsTIF defaultOemsTIF = map(defaultConnectorTIF);
            if (defaultOemsTIF == OemsTIF.TIF_UNSPECIFIED || defaultOemsTIF == OemsTIF.UNRECOGNIZED) {
                LOGGER.debug("Default TIF {} can't be mapped to OemsTIF for order type {} and venue account {}", requestedTIF, orderType, venueAccountName);
                return Optional.empty();
            }

            LOGGER.debug("TIF {} not supported for order type {} and  venue account {}, falling back to default TIF {}", requestedTIF, orderType, venueAccountName, defaultConnectorTIF);
            return Optional.of(defaultOemsTIF);
        }
    }

    private ConnectorTIF getDefaultTif(DefaultTIFs defaultTifs, OemsOrderType orderType) {
        Map<OemsOrderType, ConnectorTIF> defaultTifsMap =
            defaultTifs.getDefaultTifList().stream().collect(Collectors.toMap(tif -> map(tif.getOrderType()), tif -> tif.getTif()));

        return defaultTifsMap.get(orderType);
    }

    private static OemsOrderType map(ConnectorOrderType connectorOrderType) {
        return switch (connectorOrderType) {
            case MARKET -> OemsOrderType.MARKET;
            case LIMIT -> OemsOrderType.LIMIT;
            case STOP -> OemsOrderType.STOP;
            case STOP_LIMIT -> OemsOrderType.STOP_LIMIT;
            case ORDER_TYPE_UNSPECIFIED -> OemsOrderType.ORDER_TYPE_UNSPECIFIED;
            default -> OemsOrderType.UNRECOGNIZED;
        };
    }

    private static OemsTIF map(ConnectorTIF connectorTIF) {
        return switch (connectorTIF) {
            case GTC -> OemsTIF.GTC;
            case GTD -> OemsTIF.GTD;
            case IOC -> OemsTIF.IOC;
            case FOK -> OemsTIF.FOK;
            case DAY -> OemsTIF.DAY;
            case UNRECOGNIZED -> OemsTIF.UNRECOGNIZED;
            case TIF_UNSPECIFIED -> OemsTIF.TIF_UNSPECIFIED;
        };
    }
}
