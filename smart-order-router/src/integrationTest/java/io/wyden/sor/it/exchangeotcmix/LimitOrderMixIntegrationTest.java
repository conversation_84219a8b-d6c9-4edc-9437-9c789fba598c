package io.wyden.sor.it.exchangeotcmix;

import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

class LimitOrderMixIntegrationTest extends SORMixIntegrationTestBase {

    public static final OemsOrderType OEMS_ORDER_TYPE = OemsOrderType.LIMIT;
    public static final OemsOrderType OEMS_CHILD_ORDER_TYPE = OemsOrderType.LIMIT;

    @Test
    void limitOrderImmediateExecution() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // ER NEW for parent
        OemsResponse parentExecNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);
        assertThat(parentExecNew).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.NEW);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_NEW);
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });

        // child is filled
        collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void limitOrderDelayedExecution() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // no recommendations yet, SRE sends out heartbeat message, don't send any child orders
        smartRecommendationEngine.emitExecutionRecommendationsProcessing(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());

        // recommendations received from SRE
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is filled
        collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldSendChildOrderForSecondCandidateOnExchangeCandidateCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryOnOtcCandidateCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDoneOtcBetter(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }
    @Test
    void shouldSendChildOrderForSecondCandidateOnExchangeCandidateReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryOnOtcCandidateReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDoneOtcBetter(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldSendSecondChildWhenNoExecutionsOfFirstExchangeChild() {
        // set execution check period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderExecutionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // no executions on child order
        // ER CANCELED for child
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldSendSecondChildWhenNoExecutionsOfFirstOtcChild() {
        // set execution check period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderExecutionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDoneOtcBetter(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // no executions on child order
        // ER CANCELED for child
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldNotSendSecondChildOnPartialFillAndCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // ER CANCELED for parent
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_CANCELED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualTo("0");
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldNotSendSecondChildOnPartialFillAndReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // ER CANCELED for parent
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_CANCELED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualTo("0");
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryOnSubmissionCheckTimeoutForOtc() {
        // set submission grace period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDoneOtcBetter(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child for second candidate
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldSendSecondChildOnSubmissionCheckTimeoutForExchange() {
        // set submission grace period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child for second candidate
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldTryToCancelParentOnChildCancelReject() {
        // set submission grace period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child cancel rejected
        collider.emitExecutionReportCancelRejected(childOrder);

        // parent reported as PENDING_CANCEL
        OemsResponse parentExecPendingCancel = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_CANCEL);
        assertThat(parentExecPendingCancel).satisfies(request -> {
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PENDING_CANCEL);
        });

        // no more parent cancel requests
        collider.ensureNoOrderRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldCancelAllChildrenOnLimitOrderCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestMix(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // ER CANCELED for parent
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_CANCELED);
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });
    }


    @Test
    void shouldNotCancelParentWhenNotAllChildrenAreCanceled() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestMix(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // parent is in status pending cancel
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_CANCEL);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PENDING_CANCEL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PENDING_CANCEL);
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });

        // no ER for child cancel has been received -> parent is not canceled
        boolean statusCanceledPresent = orderGateway.isExecutionReportPresentWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(!statusCanceledPresent);
    }

    @Test
    void shouldSendNewBroadcastRequestWithOneCandidateOnCandidatesSuspensionTimeoutAndReject() throws InterruptedException {
        // set candidate suspension timeout to 4sec to be able to quickly detect it
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 4000);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected, candidate is suspended for 4sec
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // wait a little
        TimeUnit.SECONDS.sleep(2);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // wait a little (till first candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // third child is rejected, second candidate is suspended for 4sec
        collider.emitExecutionReportRejected(childOrder5);

        // broadcast request for execution recommendations to SRE with just one candidate (BINANCE)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with recommendations for BINANCE
        smartRecommendationEngine.emitExecutionRecommendationsDoneForBinance(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder6 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder6).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder6);

        // wait a little till second candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // child is rejected, candidate is suspended for 4 sec
        collider.emitExecutionReportRejected(childOrder6);

        // broadcast request for execution recommendations to SRE with just one candidate (OTC)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_OTC_INSTRUMENT_ID);
    }

    @Test
    void shouldNotSendNewBroadcastRequestBeforeCandidatesSuspensionTimeoutAndReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected, candidate is suspended for 8sec
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // third child is rejected, candidate is suspended for 8sec
        collider.emitExecutionReportRejected(childOrder5);

        // both candidates are suspended
        smartRecommendationEngine.ensureNoMoreBestExecutionRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldSendNewBroadcastRequestWithOneCandidateOnCandidatesSuspensionTimeoutAndCancel() throws InterruptedException {
        // set candidate suspension timeout to 4sec to be able to quickly detect it
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 4000);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled, candidate is suspended for 4sec
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // wait a little
        TimeUnit.SECONDS.sleep(2);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // wait a little (till first candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // third child is canceled, second candidate is suspended for 4sec
        collider.emitExecutionReportCanceled(childOrder5);

        // broadcast request for execution recommendations to SRE with just one candidate (BINANCE)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with recommendations for BINANCE
        smartRecommendationEngine.emitExecutionRecommendationsDoneForBinance(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder6 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder6).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder6);

        // wait a little till second candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // child is canceled, candidate is suspended for 4 sec
        collider.emitExecutionReportCanceled(childOrder6);

        // broadcast request for execution recommendations to SRE with just one candidate (OTC)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_OTC_INSTRUMENT_ID);
    }

    @Test
    void shouldNotSendNewBroadcastRequestBeforeCandidatesSuspensionTimeoutAndCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled, candidate is suspended for 8sec
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // third child is canceled, candidate is suspended for 8sec
        collider.emitExecutionReportCanceled(childOrder5);

        // both candidates are suspended
        smartRecommendationEngine.ensureNoMoreBestExecutionRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldSendChildOrderForSecondCandidateWhenFirstCandidateDoesNotHaveMinExecutableQuantity() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDoneWithOneSmallerQtyOtcMix(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is filled
        collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryWhenCandidatesDoNotHaveMinExecutableQuantity() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDoneWithSmallerQtyMix(bestExecutionRequest.getRecommendationSubscriptionId());

        // no child orders
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());

        // new broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest1 = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest1.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest1.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest1.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);
    }

    @Test
    void shouldCancelChildOnCancellationCheckTimeout() {
        // set submission grace period to 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 1000);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderCancellationGracePeriod times out
        // child is canceled again, take 2
        OemsRequest childCancel2 = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel2).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderCancellationGracePeriod times out
        // child is canceled again, take 3
        OemsRequest childCancel3 = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // no more child cancel retries, no more child orders, quantity is in unknown state
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldNotSendNewChildOrderOnCandidatesSuspensionTimeoutWhenParentIsFilled() {
        // set candidate suspension to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is cancelled, candidate is suspended for 8sec
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), OTC_BROKER);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_OTC_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(OTC_BROKER);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // retry is not executed
        collider.ensureNoChildOrderRequests(smartOrder.getParentOrderId());
    }

    @Test
    void shouldCancelChildOrdersOnParentFilled() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // parent is filled (i.e. on cancel replace with smaller qty)
        collider.emitExecutionReportFilledForParent(smartOrder);

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });
    }

    @Test
    void shouldExcludeNotUsedCandidatesOnReplacingSmartOrder() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // request to cancel replace parent order comes in to order gateway, it sends out cancel and then new order
        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestMix(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // new (replacing) parent order
        OemsRequest smartReplacingOrder = orderGateway.emitsOemsRequest(
                testingData.clientReplacingSmartOrderRequestMix(OEMS_ORDER_TYPE, smartOrder.getOrderId()));

        // OTC excluded to prevent execution on multiple venues, only BINANCE available
        BestExecutionRequest bestExecutionRequest1 = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartReplacingOrder.getOrderId());
        assertThat(bestExecutionRequest1.getSide()).isEqualTo(smartReplacingOrder.getSide());
        assertThat(bestExecutionRequest1.getQuantity()).isEqualTo(smartReplacingOrder.getQuantity());
        assertThat(bestExecutionRequest1.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_BINANCE_INSTRUMENT_ID);
    }

    @Test
    void shouldNotAllowRoutingCandidatesModificationOnReplacingSmartOrderInPartialFill() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMix(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_OTC_INSTRUMENT_ID, BTCUSD_BINANCE_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsMixDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // request to cancel replace parent order, order gateway sends out cancel, and then new order
        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestMix(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), BINANCE);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_BINANCE_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(BINANCE);
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // replacing order with routing candidates modified
        OemsRequest smartReplacingOrder = orderGateway.emitsOemsRequest(
                testingData.clientReplacingSmartOrderRequestForOneVenueOtcMix(OEMS_ORDER_TYPE, smartOrder.getOrderId()));

        // smart order rejected with validation error
        OemsResponse parentRejected = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartReplacingOrder.getOrderId(), OemsOrderStatus.STATUS_REJECTED);
        assertThat(parentRejected).satisfies(response -> {
            assertExecReportFor(response, smartReplacingOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.REJECTED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_REJECTED);
            assertThat(response.getReason()).isEqualTo("Smart order " + smartOrder.getOrderId() + ": cannot modify routing candidates, because smart order has been partially filled already.");
        });
    }
}
