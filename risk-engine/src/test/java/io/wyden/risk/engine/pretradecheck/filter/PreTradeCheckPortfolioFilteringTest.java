package io.wyden.risk.engine.pretradecheck.filter;

import io.wyden.published.oems.OemsRequest;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.risk.engine.pretradecheck.PreTradeCheckLevel;
import io.wyden.risk.engine.pretradecheck.PreTradeCheckRequestChannel;
import io.wyden.risk.engine.pretradecheck.wrapper.PreTradeCheckWrapper;
import io.wyden.risk.engine.referencedata.PortfolioTagsResolver;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

import static io.wyden.risk.engine.pretradecheck.filter.PreTradeCheckPortfolioFilteringTest.Scenario.checked;
import static io.wyden.risk.engine.pretradecheck.filter.PreTradeCheckPortfolioFilteringTest.Scenario.notChecked;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PreTradeCheckPortfolioFilteringTest {
    private static final String ORDER_PORTFOLIO = "SwissBank";
    private static final Map<String, String> ORDER_PORTFOLIO_TAGS = Map.of(
        "region", "EMEA",
        "country", "Switzerland"
    );

    static Stream<Scenario> scenarios() {
        return Stream.of(
            // check all portfolios:
            checked(null, null),
            checked(Set.of(), Map.of()),
            checked(Set.of(), null),
            checked(null, Map.of()),

            // portfolioId matches, tags ignored:
            checked(Set.of("SwissBank"), Map.of("region", "APAC")),
            checked(Set.of("SwissBank"), Map.of()),
            checked(Set.of("SwissBank"), null),

            // portfolioId not set, tags matter - at least one tag should match:
            checked(null, Map.of("region", "EMEA")),
            checked(null, Map.of("country", "Switzerland")),
            checked(null, Map.of("region", "EMEA", "otherTag", "someValue")),
            notChecked(null, Map.of("region", "APAC")),

            // portfolioId doesn't match, tags matter - at least one tag should match:
            checked(Set.of("GermanBank", "SpanishBank"), Map.of("region", "EMEA")),
            checked(Set.of("GermanBank", "SpanishBank"), Map.of("country", "Switzerland")),
            checked(Set.of("GermanBank", "SpanishBank"), Map.of("region", "EMEA", "otherTag", "someValue")),
            notChecked(Set.of("GermanBank", "SpanishBank"), Map.of("region", "APAC")),

            // portfolioId doesn't match, tags not configured
            notChecked(Set.of("GermanBank", "SpanishBank"), null)
        );
    }

    @ParameterizedTest(name = "portfolioFiltering{0}")
    @MethodSource("scenarios")
    void testPortfolioFilter(Scenario scenario) {
        PreTradeCheckWrapper ptc = preTradeCheck(scenario.configuredPortfolios(), scenario.configuredPortfolioTags());
        OemsRequest request = oemsRequest(ORDER_PORTFOLIO);
        PortfolioTagsResolver tagsResolver = mock(PortfolioTagsResolver.class);
        when(tagsResolver.resolveTags(anyString())).thenReturn(ORDER_PORTFOLIO_TAGS);

        final Result result;
        if (PreTradeCheckExecutionFilters.shouldCheckPortfolio(ptc, request, tagsResolver)) {
            result = Result.CHECKED;
        } else {
            result = Result.NOT_CHECKED;
        }

        assertThat(result).isEqualTo(scenario.expectedResult());
    }

    enum Result {CHECKED, NOT_CHECKED}

    record Scenario(Set<String> configuredPortfolios, Map<String, String> configuredPortfolioTags, Result expectedResult) {
        static Scenario checked(Set<String> configuredPortfolios, Map<String, String> configuredPortfolioTags) {
            return new Scenario(configuredPortfolios, configuredPortfolioTags, Result.CHECKED);
        }

        static Scenario notChecked(Set<String> configuredPortfolios, Map<String, String> configuredPortfolioTags) {
            return new Scenario(configuredPortfolios, configuredPortfolioTags, Result.NOT_CHECKED);
        }
    }

    @NotNull
    private static PreTradeCheckWrapper preTradeCheck(Set<String> portfolios, Map<String, String> portfolioTags) {
        return new PreTradeCheckWrapper(
            UUID.randomUUID().toString(),
            "Test",
            PreTradeCheckLevel.BLOCK,
            portfolios,
            portfolioTags,
            true,
            Set.of(PreTradeCheckRequestChannel.API, PreTradeCheckRequestChannel.UI),
            PreTradeCheck.newBuilder().setId("1").setType("test").setLevel(io.wyden.published.risk.PreTradeCheckLevel.BLOCK).build(),
            request -> {
                throw new IllegalStateException("This PTC should not check any orders");
            }
        );
    }

    private static OemsRequest oemsRequest(String portfolioId) {
        return OemsRequest.newBuilder().setPortfolioId(portfolioId).build();
    }
}
