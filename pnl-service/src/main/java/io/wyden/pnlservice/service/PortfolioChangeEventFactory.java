package io.wyden.pnlservice.service;

import io.wyden.published.pnl.Portfolio;
import io.wyden.published.pnl.PortfolioChangeEvent;
import io.wyden.published.pnl.PortfolioChangeEventType;

import java.util.UUID;

public class PortfolioChangeEventFactory {

    public static PortfolioChangeEvent createChangeEvent(Portfolio portfolio, PortfolioChangeEventType portfolioChangeEventType, String ownerClientId) {
        return PortfolioChangeEvent.newBuilder()
            .setMessageId(UUID.randomUUID().toString())
            .setPortfolio(portfolio)
            .setPortfolioChangeEventType(portfolioChangeEventType)
            .setOwnerClientId(ownerClientId)
            .build();
    }
}
