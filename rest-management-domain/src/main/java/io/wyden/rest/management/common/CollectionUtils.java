package io.wyden.rest.management.common;

import org.slf4j.Logger;

import java.util.Collection;
import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;

public final class CollectionUtils {

    private static final Logger LOGGER = getLogger(CollectionUtils.class);

    public static final int DEFAULT_PAGE_SIZE = 100;

    private CollectionUtils() {}

    public static <T> Collection<T> wrap(T elem) {
        if (elem == null) {
            return List.of();
        }

        return List.of(elem);
    }

    public static <T extends Enum<T>> Collection<String> wrapToString(Enum<T> elem) {
        if (elem == null) {
            return List.of();
        }

        return List.of(elem.name());
    }

    public static <T> Collection<T> wrap(Collection<T> coll) {
        if (coll == null) {
            return List.of();
        }

        return coll;
    }

    public static <T> Collection<T> wrap() {
        return wrap(null);
    }

    public static Integer wrapPageSize(Integer num) {
        if (num == null) {
            LOGGER.debug("No page size, using default={}", DEFAULT_PAGE_SIZE);
            return DEFAULT_PAGE_SIZE;
        }

        return num;
    }
}
