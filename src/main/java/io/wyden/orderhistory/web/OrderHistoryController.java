package io.wyden.orderhistory.web;

import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.service.OrderStateQueryService;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.reporting.OrderState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.NoSuchElementException;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
public class OrderHistoryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderHistoryController.class);

    private final OrderStateQueryService orderStateQueryService;

    public OrderHistoryController(OrderStateQueryService orderStateQueryService) {
        this.orderStateQueryService = orderStateQueryService;
    }

    @PostMapping(value = "/order-state-snapshots", produces = APPLICATION_PROTOBUF_VALUE, consumes = APPLICATION_JSON_VALUE)
    public Flux<OrderState> getOrderStateSnapshots(@RequestBody OrderHistorySearchInput orderHistorySearch) {
        return Flux.fromIterable(orderStateQueryService.getOrderStateSnapshots(orderHistorySearch));
    }

    @PostMapping(value = "/order-state-snapshots-paged", produces = APPLICATION_PROTOBUF_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<CursorConnection> getOrderStateSnapshotsPaged(@RequestBody OrderHistorySearchInput orderHistorySearch) {
        CursorConnection pagedOrderStateSnapshots = orderStateQueryService.getOrderStateSnapshotsPaged(orderHistorySearch);
        return ResponseEntity.ok().body(pagedOrderStateSnapshots);
    }

    @PostMapping(value = "/order-states", produces = APPLICATION_PROTOBUF_VALUE, consumes = APPLICATION_JSON_VALUE)
    public Flux<OrderState> getOrderStates(@RequestBody OrderHistorySearchInput orderHistorySearch) {
        return Flux.fromIterable(orderStateQueryService.getOrderStates(orderHistorySearch));
    }

    @PostMapping(value = "/order-states-paged", produces = APPLICATION_PROTOBUF_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<CursorConnection> getOrderStatesPaged(@RequestBody OrderHistorySearchInput orderHistorySearch) {
        CursorConnection pagedOrderStates = orderStateQueryService.getOrderStatesPaged(orderHistorySearch);
        return ResponseEntity.ok().body(pagedOrderStates);
    }

    @ExceptionHandler({NoSuchElementException.class})
    public ResponseEntity<String> noSuchElementExceptionHandler(NoSuchElementException exception) {
        LOGGER.warn("New NoSuchElementException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(exception.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException exception) {
        LOGGER.warn("New IllegalArgumentException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
    }

    @ExceptionHandler({RuntimeException.class})
    public ResponseEntity<String> runtimeExceptionHandler(RuntimeException exception) {
        LOGGER.warn("New RuntimeException {}\nReturning 500 status response code.", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exception.getMessage());
    }

}
