package io.wyden.smartrecommendationengine.service.audit;

import io.wyden.audit.client.AuditEventsClient;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.smartrecommendationengine.infra.SimpleIntegrationTestBase;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class AuditServiceIntegrationTestBase extends SimpleIntegrationTestBase {

    @TestConfiguration
    static class TestAuditServiceTestConfiguration {

        @Primary
        @Bean
        AuditEventsClient auditEventsClient(RabbitIntegrator rabbitIntegrator) {
            return new AuditEventsClient(rabbitIntegrator);
        }
    }
}
