package io.wyden.oems.storage;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.apiserver.domain.ClientRequestMapConfig;
import io.wyden.oems.storage.infra.DatabaseSetupExtension;
import io.wyden.oems.storage.infra.IntegrationTestBase;
import io.wyden.oems.storage.infra.PostgreSQLSetupExtension;
import io.wyden.published.client.ClientRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.annotation.DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD;

abstract class ClientRequestPersistenceTest extends IntegrationTestBase {

    public static final String TABLE_NAME = "client_request";

    public static final String ORDER_ID_1 = "order-1";

    static DatabaseSetupExtension DB;

    @Autowired
    private HazelcastInstance hazelcastInstance;
    private IMap<String, ClientRequest> hzMap;

    @BeforeEach
    void setUp() {
        hzMap = ClientRequestMapConfig.getMap(hazelcastInstance);
        hzMap.evictAll();
    }

    @Test
    void shouldPersistEvictFetchAndDelete() throws Exception {
        ClientRequest saved = clientRequest();
        hzMap.put(ORDER_ID_1, saved);

        DB.awaitExpectedRecordsInTable(TABLE_NAME, 1);
        DB.logTableContent(TABLE_NAME);

        hzMap.evictAll();
        assertThat(hzMap.size()).isEqualTo(0);
        ClientRequest fetched = hzMap.get(ORDER_ID_1);
        assertThat(fetched).isEqualTo(saved);

        hzMap.delete(ORDER_ID_1);
        DB.awaitExpectedRecordsInTable(TABLE_NAME, 0);
        assertThat(hzMap.size()).isEqualTo(0);
    }

    private static ClientRequest clientRequest() {
        return ClientRequest.newBuilder()
            .setOrderId(ORDER_ID_1)
            .build();
    }
}

@ExtendWith(PostgreSQLSetupExtension.class)
@DirtiesContext(classMode = BEFORE_EACH_TEST_METHOD)
class PostgreSqlClientRequestPersistenceTest extends ClientRequestPersistenceTest {

}
