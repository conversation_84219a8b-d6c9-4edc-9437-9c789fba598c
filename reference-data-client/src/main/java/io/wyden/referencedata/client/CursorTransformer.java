package io.wyden.referencedata.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;

import java.util.Base64;

public class CursorTransformer {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String encodePortfolioCursor(GetPortfoliosRequestDto.SortBy sortBy, Portfolio portfolio) {
        PortfolioCursor cursor = getPortfolioCursor(sortBy, portfolio);
        return encodeCursor(cursor);
    }

    private static String encodeCursor(PortfolioCursor portfolioCursor) {
        try {
            String jsonCursor = objectMapper.writeValueAsString(portfolioCursor);
            return Base64.getUrlEncoder().withoutPadding().encodeToString(jsonCursor.getBytes());
        } catch (Exception e) {
            throw new RuntimeException("Error encoding cursor", e);
        }
    }

    public static PortfolioCursor decodePortfolioCursor(String encodedCursor) {
        return decodeCursor(encodedCursor, PortfolioCursor.class);
    }

    public static <T> T decodeCursor(String encodedCursor, Class<T> decodeClass) {
        try {
            byte[] decodedBytes = Base64.getUrlDecoder().decode(encodedCursor);
            String jsonCursor = new String(decodedBytes);
            return objectMapper.readValue(jsonCursor, decodeClass);
        } catch (Exception e) {
            throw new RuntimeException("Error decoding cursor", e);
        }
    }

    public static boolean isPortfolioCursorValid(GetPortfoliosRequestDto request) {
        try {
            PortfolioCursor portfolioCursor = decodePortfolioCursor(request.after());

            if (request.sortBy() == null || request.sortBy() == GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME) {
                return portfolioCursor.isSortByPortfolioName();
            } else if (request.sortBy() == GetPortfoliosRequestDto.SortBy.PORTFOLIO_ID) {
                return portfolioCursor.isSortByPortfolioId();
            } else if (request.sortBy() == GetPortfoliosRequestDto.SortBy.CREATED_AT) {
                return portfolioCursor.isSortByCreatedAt();
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private static PortfolioCursor getPortfolioCursor(GetPortfoliosRequestDto.SortBy sortBy, Portfolio portfolio) {
        if (sortBy == null || sortBy == GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME) {
            return new PortfolioCursor(portfolio.getCreatedAt(), portfolio.getName(), null);
        } else if (sortBy == GetPortfoliosRequestDto.SortBy.PORTFOLIO_ID) {
            return new PortfolioCursor(null, null, portfolio.getId());
        } else if (sortBy == GetPortfoliosRequestDto.SortBy.CREATED_AT) {
            return new PortfolioCursor(portfolio.getCreatedAt(), null, null);
        }

        return new PortfolioCursor(null, null, null);
    }
}
