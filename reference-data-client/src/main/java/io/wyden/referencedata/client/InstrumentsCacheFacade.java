package io.wyden.referencedata.client;

import com.hazelcast.map.IMap;
import com.hazelcast.query.PagingPredicate;
import com.hazelcast.query.Predicate;
import com.hazelcast.query.Predicates;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import io.wyden.referencedata.domain.InstrumentMapConfig;
import io.wyden.referencedata.domain.model.GetInstrumentsRequestDto;
import io.wyden.referencedata.domain.model.SortingOrder;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class InstrumentsCacheFacade {

    private final IMap<String, Instrument> instrumentsMap;

    private final Tracing otlTracing;

    public InstrumentsCacheFacade(IMap<String, Instrument> instrumentsMap, Tracing otlTracing) {
        this.instrumentsMap = instrumentsMap;
        this.otlTracing = otlTracing;
    }

    public Optional<Instrument> find(String instrumentId) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.find", SpanKind.CLIENT)) {
            Instrument instrument = instrumentsMap.get(instrumentId);
            return Optional.ofNullable(instrument);
        }
    }

    public Collection<Instrument> findAll() {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findall", SpanKind.CLIENT)) {
            return instrumentsMap.values();
        }
    }

    public Collection<Instrument> findAllByVenueName(Collection<String> venueNames) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbyvenuename", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterByVenueName(venueNames));
        }
    }

    public Collection<Instrument> findAllByVenueType(VenueType venueType) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbyvenuetype", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterByVenueType(venueType));
        }
    }

    public Collection<Instrument> findAllByBaseCurrencyQuoteCurrencyVenueType(String baseCurrency, String quoteCurrency, VenueType venueType) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbyvenuetypebasequotecurrency", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterByBaseCurrencyQuoteCurrencyVenueType(baseCurrency, quoteCurrency, venueType));
        }
    }

    public Collection<Instrument> findAllByBaseCurrencyQuoteCurrencyVenueName(String baseCurrency, String quoteCurrency, String venueName) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbyvenuenamebasequotecurrency", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterByBaseCurrencyQuoteCurrencyVenueName(baseCurrency, quoteCurrency, venueName));
        }
    }

    public Collection<Instrument> findAllByBaseCurrencyQuoteCurrency(String baseCurrency, String quoteCurrency) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbyvenuetypebasequotecurrency", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterByBaseCurrencyQuoteCurrency(baseCurrency, quoteCurrency));
        }
    }

    public Collection<Instrument> findAllBySymbolAssetClassVenueType(String symbol, AssetClass assetClass, VenueType venueType) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbysymbolassetclassvenuetype", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterBySymbolInstrumentTypeVenueType(symbol, assetClass, venueType));
        }
    }

    public Collection<Instrument> findAllBySymbolAssetClassVenueName(String symbol, AssetClass assetClass, String venueName) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.findallbysymbolassetclassvenuename", SpanKind.CLIENT)) {
            return instrumentsMap.values(InstrumentMapConfig.filterBySymbolInstrumentTypeVenueName(symbol, assetClass, venueName));
        }
    }

    public CursorConnection search(GetInstrumentsRequestDto request) {

        // all instruments matching search request
        List<Predicate<String, Instrument>> predicates = buildPredicate(request);

        Predicate<String, Instrument> countPredicate = Predicates.and(predicates.toArray(new Predicate[0]));
        long total = instrumentsMap.keySet(countPredicate).size();

        if (isNotBlank(request.after())) {
            if (request.sortingOrder() == SortingOrder.ASC) {
                Predicate<String, Instrument> afterPredicate = InstrumentMapConfig.filterByInstrumentIdAfter(request.after());
                predicates.add(afterPredicate);
            } else {
                Predicate<String, Instrument> beforePredicate = InstrumentMapConfig.filterByInstrumentIdBefore(request.after());
                predicates.add(beforePredicate);
            }
        }

        // all instruments 'after' requested cursor (instrumentId)
        Predicate<String, Instrument> searchPredicate = Predicates.and(predicates.toArray(new Predicate[0]));
        int remaining = instrumentsMap.keySet(searchPredicate).size();

        PagingPredicate<String, Instrument> pagingPredicate = Predicates.pagingPredicate(
            searchPredicate,
            request.sortingComparator(),
            request.pageSize());

        Collection<Instrument> instruments = instrumentsMap.values(pagingPredicate);

        return PaginationWrapper.wrapToProto(instruments,
            instrument -> instrument.getInstrumentIdentifiers().getInstrumentId(),
            instrument -> CursorNode.newBuilder().setInstrument(instrument).build(),
            remaining,
            total);
    }

    private static List<Predicate<String, Instrument>> buildPredicate(GetInstrumentsRequestDto request) {
        List<Predicate<String, Instrument>> predicates = new LinkedList<>();

        if (CollectionUtils.isNotEmpty(request.venueNames())) {
            Predicate<String, Instrument> idPredicate = InstrumentMapConfig.filterByVenueName(request.venueNames());
            predicates.add(idPredicate);
        }

        if (isNotBlank(request.instrumentId())) {
            Predicate<String, Instrument> namePredicate = InstrumentMapConfig.filterByInstrumentIdContains(request.instrumentId());
            predicates.add(namePredicate);
        }

        if (Objects.nonNull(request.instrumentIdsPredicate()) && CollectionUtils.isNotEmpty(request.instrumentIdsPredicate().instrumentIds())) {
            Predicate<String, Instrument> instrumentIdsPredicate = InstrumentMapConfig.filterByInstrumentIdPredicate(request.instrumentIdsPredicate());
            predicates.add(instrumentIdsPredicate);
        }

        if (request.venueType() != null) {
            Predicate<String, Instrument> venueTypePredicate = InstrumentMapConfig.filterByVenueType(request.venueType());
            predicates.add(venueTypePredicate);
        }

        if (request.archived() != null) {
            Predicate<String, Instrument> archivedPredicate = InstrumentMapConfig.filterByArchived(request.archived());
            predicates.add(archivedPredicate);
        }

        if (request.tradeable() != null) {
            Predicate<String, Instrument> tradeablePredicate = InstrumentMapConfig.filterByTradeable(request.tradeable());
            predicates.add(tradeablePredicate);
        }

        return predicates;
    }
}
