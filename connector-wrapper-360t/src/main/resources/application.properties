spring.application.name=360t-connector
spring.profiles.active = prod

spring.cloud.vault.enabled=true
spring.cloud.vault.scheme=http

server.port = 8112

tracing.resource.service=connector-wrapper-360t
tracing.resource.scope=io.wyden.connector._360t
management.metrics.tags.wyden_service=connector-wrapper-360t

account.name=c360t
venue.name=360T

connector360t.fix.socketKeyStore=classpath:/star.360t.com.jks

connector360t.fix.session.t.senderCompId=
connector360t.fix.session.md.senderCompId=
connector360t.fix.session.t.senderSubId=
connector360t.fix.session.md.senderSubId=
connector360t.fix.session.t.senderLocationId=
connector360t.fix.session.md.senderLocationId=
connector360t.fix.session.t.password=
connector360t.fix.session.md.password=

# time-out after which market data subscription is considered inactive, if no market data received
# unit: seconds
subscription.timeout=5
