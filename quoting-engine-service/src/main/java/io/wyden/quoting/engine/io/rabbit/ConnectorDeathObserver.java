package io.wyden.quoting.engine.io.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.published.targetregistry.AggregatedTargetStateChangedEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

@Service
public class ConnectorDeathObserver {

    private final Sinks.Many<AggregatedTargetStateChangedEvent> sink = Sinks.many().multicast().directBestEffort();

    public ConnectorDeathObserver(RabbitExchange<AggregatedTargetStateChangedEvent> rabbitExchange,
                                  RabbitQueue<AggregatedTargetStateChangedEvent> aggregatedTargetStateChangedEventQueue) {

        aggregatedTargetStateChangedEventQueue.attachConsumerAutoAck((body, type) -> AggregatedTargetStateChangedEvent.parseFrom(body), (event, props) -> {
            if (event.getCapabilitiesList().stream().anyMatch(ConnectorDeathObserver::marketDataDead)) {
                sink.tryEmitNext(event);
            }
        });

        aggregatedTargetStateChangedEventQueue.bindWithRoutingKey(rabbitExchange, "");
    }

    private static boolean marketDataDead(AggregatedTargetStateChangedEvent.CapabilityState c) {
        return c.getCapability().equals(Capability.MARKET_DATA) && !c.getHealth().getStatus().equals(HealthStatus.ALIVE);
    }

    public Flux<AggregatedTargetStateChangedEvent> getMarketDataDeadNotifications() {
        return sink.asFlux();
    }
}
