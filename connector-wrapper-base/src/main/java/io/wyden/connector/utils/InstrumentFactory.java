package io.wyden.connector.utils;

import ch.algotrader.api.connector.referencedata.TradingConstraints;
import ch.algotrader.api.domain.security.ForexDTO;
import ch.algotrader.api.domain.security.SecurityIdentity;

import io.wyden.published.referencedata.InstrumentIdentifiers;

public class InstrumentFactory {

    public static ForexDTO getForexDTO(InstrumentIdentifiers order) {
        String adapterTicker = order.getAdapterTicker();
        String instrumentId = order.getInstrumentId();
        String[] split = instrumentId.split("@");
        String subSymbol = split[0];
        return stubForex(subSymbol, adapterTicker);
    }

    private static ForexDTO stubForex(String subSymbol, String adapterTicker) {
        return ForexDTO.builder()
                .setSecurityIdentity(SecurityIdentity.builder()
                        .with("symbol", subSymbol)
                        .with("adapterTicker", adapterTicker)
                        .create()
                )
                .setTradingConstraints(TradingConstraints.newBuilder()
                        .setIsTradeable(true)
                        .build())
                .create();
    }
}
