package io.wyden.marketdata.web;

import io.wyden.marketdata.connector.ConnectorSubscriptionService;
import io.wyden.published.marketdata.InstrumentKey;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/config")
public class ConfigController {

    private final ConnectorSubscriptionService connectorSubscriptionService;

    public ConfigController(ConnectorSubscriptionService connectorSubscriptionService) {
        this.connectorSubscriptionService = connectorSubscriptionService;
    }

    @PostMapping("/clearSubs")
    @ResponseStatus(HttpStatus.OK)
    public void clearSubs(@RequestBody ClearMarketDataRequest clearMarketDataRequest) {
        List<InstrumentKey> instrumentKeyList = clearMarketDataRequest.securitiesToRemove().stream()
            .map(securityDto -> InstrumentKey.newBuilder().setInstrumentId(securityDto.instrumentId()).setVenueAccount(securityDto.venueAccount()).build())
            .toList();
        connectorSubscriptionService.removeSubscriptions(instrumentKeyList);
    }
}
